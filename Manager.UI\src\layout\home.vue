<template>
  <div class="chat-container">
    <div class="chat-messages" ref="chatMessages">
      <!-- 消息列表 -->
      <template v-for="(message, index) in messages" :key="index">
        <!-- 用户消息 -->
        <div v-if="message.type === 'user'" class="message user">
          <div class="avatar">我</div>
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
          </div>
        </div>

        <!-- 助手消息 -->
        <div v-else-if="message.type === 'assistant'" class="message assistant">
          <div class="avatar">助</div>
          <div class="message-content">
            <!-- 如果有内容则显示内容，否则显示加载动画 -->
            <div v-if="message.content && message.content.trim()" class="message-text" v-html="message.content"></div>
            <div v-else class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </template>

      <!-- 不再使用单独的加载动画 -->
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <textarea
        ref="messageInput"
        v-model="inputMessage"
        placeholder="请输入你的问题..."
        :rows="1"
        @input="autoResize"
        @keydown.enter.prevent="handleEnterKey"
        :disabled="loading"
      ></textarea>
      <el-button
        @click="sendMessage"
        circle
        :icon="Promotion"
        :disabled="loading || !inputMessage.trim()"
        :loading="loading"
      ></el-button>
    </div>
  </div>
</template>

<script setup>
import { Promotion } from '@element-plus/icons-vue'
</script>

<script>

import { nextTick } from 'vue'
import { sendChatMessage } from '@/api/chat/chat'

export default {
  data() {
    return {
      inputMessage: '',
      messages: [
        {
          type: 'assistant',
          content: '你好！我是AI助手，很高兴为你服务。请问有什么我可以帮你的吗？'
        }
      ],
      loading: false,
      currentAssistantMessage: null
    }
  },

  methods: {
    handleEnterKey(event) {
      // 如果按下Shift+Enter，则允许换行
      if (event.shiftKey) {
        return;
      }
      // 否则发送消息
      this.sendMessage();
    },

    async sendMessage(retryCount = 0) {
      // 检查消息是否为空
      if (!this.inputMessage.trim() || this.loading) {
        return;
      }

      // 添加用户消息到消息列表
      const userMessage = this.inputMessage.trim();
      this.messages.push({
        type: 'user',
        content: userMessage
      });

      // 清空输入框
      this.inputMessage = '';

      // 重置文本区域高度
      this.$nextTick(() => {
        this.resetTextareaHeight();
      });

      // 滚动到底部
      this.scrollToBottom();

      // 设置加载状态，显示加载动画
      this.loading = true;

      // 创建一个新的助手消息对象
      this.currentAssistantMessage = {
        type: 'assistant',
        content: ''
      };

      // 添加到消息列表
      this.messages.push(this.currentAssistantMessage);

      try {
        // 发送消息到服务器，使用回调函数处理响应
        console.log('发送消息到服务器:', userMessage);
        const result = await sendChatMessage(userMessage, this.handleStreamResponse);

        console.log('发送消息结果:', result);

        // 如果消息内容为空，可能是没有正确接收到响应
        if (this.currentAssistantMessage && (!this.currentAssistantMessage.content || !this.currentAssistantMessage.content.trim())) {
          console.warn('未收到有效响应内容');

          // 重试逻辑 - 最多重试1次
          if (retryCount < 1) {
            console.log(`尝试第 ${retryCount + 1} 次重试...`);

            // 移除上一次添加的空消息
            const lastIndex = this.messages.length - 1;
            if (lastIndex >= 0 && this.messages[lastIndex] === this.currentAssistantMessage) {
              this.messages.pop();
            }

            // 短暂延迟后重试
            setTimeout(() => {
              this.loading = false;
              this.currentAssistantMessage = null;
              this.sendMessage(retryCount + 1);
            }, 1000);

            return;
          } else {
            // 如果重试后仍然失败，显示错误信息
            this.currentAssistantMessage.content = '抱歉，我遇到了一些问题，无法回答您的问题。请稍后再试。';
          }
        }
      } catch (error) {
        console.error('处理消息时出错:', error);

        // 如果消息内容为空，显示错误信息
        if (this.currentAssistantMessage && (!this.currentAssistantMessage.content || !this.currentAssistantMessage.content.trim())) {
          this.currentAssistantMessage.content = '抱歉，我遇到了一些问题，无法回答您的问题。请稍后再试。';
        }
      } finally {
        // 取消加载状态
        this.loading = false;
        this.currentAssistantMessage = null;
      }
    },

    // 特殊标记已在 chat.js 中处理

    // 处理流式响应的增量更新
    handleStreamResponse(data) {
      console.log('收到响应数据:', data);

      if (!this.currentAssistantMessage) return;

      // 处理数据
      if (data) {
        try {
          // 检查是否是思考内容
          const isThinking = data.includes('<think>');
          const isEndThinking = data.includes('</think>');

          // 如果包含思考标签，跳过这部分内容
          if (isThinking && !isEndThinking) {
            console.log('跳过思考内容');
            return;
          }

          // 如果是思考结束，提取思考后的实际内容
          if (isEndThinking) {
            const endThinkIndex = data.indexOf('</think>');
            if (endThinkIndex > -1) {
              // 提取思考标签后的内容
              data = data.substring(endThinkIndex + 8); // 8 是 '</think>' 的长度
              console.log('思考结束，提取后续内容:', data);
            }
          }

          // 如果提取到内容，更新消息（增量更新）
          if (data) {
            console.log('提取到内容:', data);

            // 直接累加内容，因为是流式响应的增量更新
            // 特殊标记和换行符的处理已在 chat.js 中完成
            this.currentAssistantMessage.content += data;
            this.scrollToBottom();
          }
        } catch (error) {
          console.error('处理响应数据时出错:', error);

          // 出错时也尝试显示原始内容
          if (data) {
            this.currentAssistantMessage.content += data;
            this.scrollToBottom();
          }
        }
      }
    },

    scrollToBottom() {
      nextTick(() => {
        const chatMessages = this.$refs.chatMessages;
        if (chatMessages) {
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }
      });
    },

    autoResize() {
      const textarea = this.$refs.messageInput;
      if (!textarea) return;

      // 如果没有内容，使用默认高度并隐藏滚动条
      if (!this.inputMessage.trim()) {
        this.resetTextareaHeight();
        return;
      }

      // 重置高度，以便正确计算
      textarea.style.height = 'auto';

      // 计算新高度
      const scrollHeight = textarea.scrollHeight;

      // 如果内容高度超过最大高度，显示滚动条
      if (scrollHeight > 120) {
        textarea.style.height = '120px';
        textarea.style.overflowY = 'auto';
      } else {
        // 否则使用内容高度并隐藏滚动条
        textarea.style.height = scrollHeight + 'px';
        textarea.style.overflowY = 'hidden';
      }
    },

    resetTextareaHeight() {
      const textarea = this.$refs.messageInput;
      if (!textarea) return;

      // 重置为默认高度
      textarea.style.height = '40px';
      textarea.style.overflowY = 'hidden';
    }
  },

  mounted() {
    debugger
    // 初始滚动到底部
    this.scrollToBottom();

    // 初始化文本区域高度
    this.$nextTick(() => {
      this.autoResize();
    });
  }
}
</script>

<style>
/* 使用全局样式，不需要重复定义 */
/* 只添加组件特定的样式覆盖 */

.user .message-content {
  background: var(--primary-color);
  color: white;
}

.user .avatar {
  background: var(--primary-color);
  color: white;
}

/* 消息内容中的加载动画 */
.message-content .typing-indicator {
  display: flex;
  align-items: center;
  padding: 0;
  background: transparent;
}

.message-content .typing-indicator span {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: var(--text-secondary);
  border-radius: 50%;
  margin-right: 5px;
  animation: typing 1s infinite;
}

.message-content .typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.message-content .typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

.chat-input textarea {
  resize: none;
  min-height: 40px;
  max-height: 120px; /* 大约5行的高度 */
  overflow-y: hidden; /* 默认隐藏滚动条 */
  box-sizing: border-box;
  padding: 10px;
  line-height: 1.5;
}
@keyframes typing {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

/* 媒体查询 */
@media (max-width: 768px) {
  .chat-container {
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }

  .message-content {
    max-width: 85%;
  }
}
</style>