runtime-core.esm-bundler.js:4634 Feature flag __VUE_PROD_HYDRATION_MISMATCH_DETAILS__ is not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.
initFeatureFlags @ runtime-core.esm-bundler.js:4634
baseCreateRenderer @ runtime-core.esm-bundler.js:4651
createRenderer @ runtime-core.esm-bundler.js:4644
ensureRenderer @ runtime-dom.esm-bundler.js:1744
createApp @ runtime-dom.esm-bundler.js:1758
（匿名） @ main.js?t=1750769668075:12
vue-router.mjs:51 [Vue Router warn]: No match found for location with path "/menu"
warn @ vue-router.mjs:51
resolve @ vue-router.mjs:3161
pushWithRedirect @ vue-router.mjs:3295
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750769668075:46
App.vue:12 初始化主题: light
user.js?t=1750769668075:163 路由守卫开始执行: /menu
user.js?t=1750769668075:180 获取到的 token: 存在 string
user.js?t=1750769668075:199 路由守卫检查: {toPath: '/menu', fromPath: '/', hasToken: true, logined: false, tokenExists: true}
user.js?t=1750769668075:217 有 token 但未登录，尝试加载用户信息
user.js?t=1750769668075:60 onLoad 开始执行，toPath: /menu
user.js?t=1750769668075:80 从 Pinia 获取token: {hasToken: true, tokenType: 'string'}
user.js?t=1750769668075:84 开始获取用户信息和权限...
user.js?t=1750769668075:89 用户信息和权限获取成功: {user: {…}, permissions: Array(95)}
user.js?t=1750769668075:100 筛选出的菜单权限数量: 29
user.js?t=1750769668075:112 ✅ 添加路由: /community
user.js?t=1750769668075:112 ✅ 添加路由: /org
user.js?t=1750769668075:112 ✅ 添加路由: /member
user.js?t=1750769668075:112 ✅ 添加路由: /goodsList
user.js?t=1750769668075:112 ✅ 添加路由: /orderList
user.js?t=1750769668075:112 ✅ 添加路由: /job
user.js?t=1750769668075:112 ✅ 添加路由: /dept
user.js?t=1750769668075:112 ✅ 添加路由: /person
user.js?t=1750769668075:112 ✅ 添加路由: /position
user.js?t=1750769668075:112 ✅ 添加路由: /visiror
user.js?t=1750769668075:112 ✅ 添加路由: /workorder
user.js?t=1750769668075:112 ✅ 添加路由: /user
user.js?t=1750769668075:112 ✅ 添加路由: /building
user.js?t=1750769668075:112 ✅ 添加路由: /role
user.js?t=1750769668075:112 ✅ 添加路由: /goods
user.js?t=1750769668075:112 ✅ 添加路由: /resident
user.js?t=1750769668075:112 ✅ 添加路由: /paymentItemsList
user.js?t=1750769668075:112 ✅ 添加路由: /menu
user.js?t=1750769668075:112 ✅ 添加路由: /vehicleList
user.js?t=1750769668075:112 ✅ 添加路由: /propertyBillList
user.js?t=1750769668075:112 ✅ 添加路由: /dict
user.js?t=1750769668075:114 ❌ 组件未找到: ../../views/community/list.vue 菜单: 小区管理
onLoad @ user.js?t=1750769668075:114
await in onLoad
（匿名） @ user.js?t=1750769668075:219
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750769668075:46
user.js?t=1750769668075:112 ✅ 添加路由: /activity
user.js?t=1750769668075:112 ✅ 添加路由: /noticeList
user.js?t=1750769668075:112 ✅ 添加路由: /imagetext
user.js?t=1750769668075:143 构建的菜单树: (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}]
user.js?t=1750769668075:157 onLoad 执行完成，logined: true
user.js?t=1750769668075:221 用户信息加载成功，继续导航
user.js?t=1750769668075:295 应用启动检查: {hasToken: true, hasPermissions: true, logined: true}
user.js?t=1750769668075:299 应用启动时恢复动态路由...
user.js?t=1750769668075:310 🚀 应用启动添加路由: /community
user.js?t=1750769668075:310 🚀 应用启动添加路由: /org
user.js?t=1750769668075:310 🚀 应用启动添加路由: /member
user.js?t=1750769668075:310 🚀 应用启动添加路由: /goodsList
user.js?t=1750769668075:310 🚀 应用启动添加路由: /orderList
user.js?t=1750769668075:310 🚀 应用启动添加路由: /job
user.js?t=1750769668075:310 🚀 应用启动添加路由: /dept
user.js?t=1750769668075:310 🚀 应用启动添加路由: /person
user.js?t=1750769668075:310 🚀 应用启动添加路由: /position
user.js?t=1750769668075:310 🚀 应用启动添加路由: /visiror
user.js?t=1750769668075:310 🚀 应用启动添加路由: /workorder
user.js?t=1750769668075:310 🚀 应用启动添加路由: /user
user.js?t=1750769668075:310 🚀 应用启动添加路由: /building
user.js?t=1750769668075:310 🚀 应用启动添加路由: /role
user.js?t=1750769668075:310 🚀 应用启动添加路由: /goods
user.js?t=1750769668075:310 🚀 应用启动添加路由: /resident
user.js?t=1750769668075:310 🚀 应用启动添加路由: /paymentItemsList
user.js?t=1750769668075:310 🚀 应用启动添加路由: /menu
user.js?t=1750769668075:310 🚀 应用启动添加路由: /vehicleList
user.js?t=1750769668075:310 🚀 应用启动添加路由: /propertyBillList
user.js?t=1750769668075:310 🚀 应用启动添加路由: /dict
user.js?t=1750769668075:312 ❌ 应用启动: 组件未找到: ../../views/community/list.vue
（匿名） @ user.js?t=1750769668075:312
setTimeout
（匿名） @ user.js?t=1750769668075:291
user.js?t=1750769668075:310 🚀 应用启动添加路由: /activity
user.js?t=1750769668075:310 🚀 应用启动添加路由: /noticeList
user.js?t=1750769668075:310 🚀 应用启动添加路由: /imagetext
user.js?t=1750769668075:343 应用启动时恢复登录状态和菜单树