可用的组件模块: (30) ['../../views/activity/list.vue', '../../views/community/communityBuildingList.vue', '../../views/community/communityList.vue', '../../views/community/communityResidentList.vue', '../../views/community/communityVehicleList.vue', '../../views/goods/list.vue', '../../views/goods/orderList.vue', '../../views/imagetext/imagetextList.vue', '../../views/job/list.vue', '../../views/login/login.vue', '../../views/notice/noticeList.vue', '../../views/property/billList.vue', '../../views/property/paymentItemsList.vue', '../../views/property/personList.vue', '../../views/system/deptList.vue', '../../views/system/dictList.vue', '../../views/system/menuList.vue', '../../views/system/miniUserList.vue', '../../views/system/organizational.vue', '../../views/system/personList.vue', '../../views/system/positionList.vue', '../../views/system/roleList.vue', '../../views/system/userList.vue', '../../views/test/OrgFunctionTest.vue', '../../views/test/routeTest.vue', '../../views/test/storageTest.vue', '../../views/visitor/list.vue', '../../views/workOrder/workOrderList.vue', '../../views/job/components/JobDetail.vue', '../../views/job/components/JobEdit.vue']
runtime-core.esm-bundler.js:4634 Feature flag __VUE_PROD_HYDRATION_MISMATCH_DETAILS__ is not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.
initFeatureFlags @ runtime-core.esm-bundler.js:4634
baseCreateRenderer @ runtime-core.esm-bundler.js:4651
createRenderer @ runtime-core.esm-bundler.js:4644
ensureRenderer @ runtime-dom.esm-bundler.js:1744
createApp @ runtime-dom.esm-bundler.js:1758
（匿名） @ main.js?t=1750768869046:12
vue-router.mjs:51 [Vue Router warn]: No match found for location with path "/org"
warn @ vue-router.mjs:51
resolve @ vue-router.mjs:3161
pushWithRedirect @ vue-router.mjs:3295
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750768869046:46
App.vue:12 初始化主题: light
user.js?t=1750768869046:170 路由守卫开始执行: /org
user.js?t=1750768869046:187 获取到的 token: 存在 string
user.js?t=1750768869046:206 路由守卫检查: {toPath: '/org', fromPath: '/', hasToken: true, logined: false, tokenExists: true}
user.js?t=1750768869046:224 有 token 但未登录，尝试加载用户信息
user.js?t=1750768869046:57 onLoad 开始执行，toPath: /org
user.js?t=1750768869046:77 从 Pinia 获取token: {hasToken: true, tokenType: 'string'}
user.js?t=1750768869046:81 开始获取用户信息和权限...
user.js?t=1750768869046:86 用户信息和权限获取成功: {user: {…}, permissions: Array(96)}
2user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
2user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
2user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 小区信息 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 组织管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 微信用户 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 好物列表 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 订单管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 工单列表 menuType: 
user.js?t=1750768869046:97 检查菜单项: 定时任务 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 部门管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 员工管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 职位管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
2user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
2user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 立即运行 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 审核 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查看 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 查询 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 添加 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 编辑 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 删除 menuType: permis
user.js?t=1750768869046:97 检查菜单项: 小区访客 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 小区工单 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 系统管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 用户管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 小区楼房 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 角色管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 好物管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 小区住户 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 缴费项目 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 菜单管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 小区车辆 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 物业缴费 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 物业账单 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 数据字典 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 小区管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 消息中心 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 工单管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 活动管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 访客管理 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 消息通知 menuType: menu
user.js?t=1750768869046:97 检查菜单项: 图文管理 menuType: menu
user.js?t=1750768869046:100 筛选出的菜单权限: (30) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
user.js?t=1750768869046:101 原始权限数据: (96) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
user.js?t=1750768869046:108 尝试添加路由: {menuName: '小区信息', path: '/community', componentPath: '../../views/community/communityList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /community
user.js?t=1750768869046:108 尝试添加路由: {menuName: '组织管理', path: '/org', componentPath: '../../views/system/organizational.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /org
user.js?t=1750768869046:108 尝试添加路由: {menuName: '微信用户', path: '/member', componentPath: '../../views/system/miniUserList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /member
user.js?t=1750768869046:108 尝试添加路由: {menuName: '好物列表', path: '/goodsList', componentPath: '../../views/goods/list.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /goodsList
user.js?t=1750768869046:108 尝试添加路由: {menuName: '订单管理', path: '/orderList', componentPath: '../../views/goods/orderList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /orderList
user.js?t=1750768869046:108 尝试添加路由: {menuName: '定时任务', path: '/job', componentPath: '../../views/job/list.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /job
user.js?t=1750768869046:108 尝试添加路由: {menuName: '部门管理', path: '/dept', componentPath: '../../views/system/deptList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /dept
user.js?t=1750768869046:108 尝试添加路由: {menuName: '员工管理', path: '/person', componentPath: '../../views/system/personList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /person
user.js?t=1750768869046:108 尝试添加路由: {menuName: '职位管理', path: '/position', componentPath: '../../views/system/positionList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /position
user.js?t=1750768869046:108 尝试添加路由: {menuName: '小区访客', path: '/visiror', componentPath: '../../views/community/visitorList.vue', componentFound: false}
user.js?t=1750768869046:121 组件未找到: ../../views/community/visitorList.vue
onLoad @ user.js?t=1750768869046:121
await in onLoad
（匿名） @ user.js?t=1750768869046:226
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750768869046:46
user.js?t=1750768869046:108 尝试添加路由: {menuName: '小区工单', path: '/workorder', componentPath: '../../views/community/workorderList.vue', componentFound: false}
user.js?t=1750768869046:121 组件未找到: ../../views/community/workorderList.vue
onLoad @ user.js?t=1750768869046:121
await in onLoad
（匿名） @ user.js?t=1750768869046:226
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750768869046:46
user.js?t=1750768869046:108 尝试添加路由: {menuName: '用户管理', path: '/user', componentPath: '../../views/system/userList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /user
user.js?t=1750768869046:108 尝试添加路由: {menuName: '小区楼房', path: '/building', componentPath: '../../views/community/communityBuildingList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /building
user.js?t=1750768869046:108 尝试添加路由: {menuName: '角色管理', path: '/role', componentPath: '../../views/system/roleList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /role
user.js?t=1750768869046:108 尝试添加路由: {menuName: '好物管理', path: '/goods', componentPath: '../../views/goods/list.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /goods
user.js?t=1750768869046:108 尝试添加路由: {menuName: '小区住户', path: '/resident', componentPath: '../../views/community/communityResidentList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /resident
user.js?t=1750768869046:108 尝试添加路由: {menuName: '缴费项目', path: '/paymentItemsList', componentPath: '../../views/property/paymentItemsList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /paymentItemsList
user.js?t=1750768869046:108 尝试添加路由: {menuName: '菜单管理', path: '/menu', componentPath: '../../views/system/menuList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /menu
user.js?t=1750768869046:108 尝试添加路由: {menuName: '小区车辆', path: '/vehicleList', componentPath: '../../views/community/communityVehicleList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /vehicleList
user.js?t=1750768869046:108 尝试添加路由: {menuName: '物业账单', path: '/propertyBillList', componentPath: '../../views/property/billList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /propertyBillList
user.js?t=1750768869046:108 尝试添加路由: {menuName: '数据字典', path: '/dict', componentPath: '../../views/system/dictList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /dict
user.js?t=1750768869046:108 尝试添加路由: {menuName: '小区管理', path: '/community', componentPath: '../../views/community/list.vue', componentFound: false}
user.js?t=1750768869046:121 组件未找到: ../../views/community/list.vue
onLoad @ user.js?t=1750768869046:121
await in onLoad
（匿名） @ user.js?t=1750768869046:226
（匿名） @ vue-router.mjs:2107
runWithContext @ vue-router.mjs:2074
（匿名） @ vue-router.mjs:2107
（匿名） @ vue-router.mjs:2079
runWithContext @ runtime-core.esm-bundler.js:4023
runWithContext @ vue-router.mjs:3387
（匿名） @ vue-router.mjs:3743
Promise.then
（匿名） @ vue-router.mjs:3743
runGuardQueue @ vue-router.mjs:3743
（匿名） @ vue-router.mjs:3413
Promise.then
navigate @ vue-router.mjs:3406
pushWithRedirect @ vue-router.mjs:3327
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
（匿名） @ main.js?t=1750768869046:46
user.js?t=1750768869046:108 尝试添加路由: {menuName: '活动管理', path: '/activity', componentPath: '../../views/activity/list.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /activity
user.js?t=1750768869046:108 尝试添加路由: {menuName: '访客管理', path: '/visitor', componentPath: '../../views/visitor/list.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /visitor
user.js?t=1750768869046:108 尝试添加路由: {menuName: '消息通知', path: '/noticeList', componentPath: '../../views/notice/noticeList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /noticeList
user.js?t=1750768869046:108 尝试添加路由: {menuName: '图文管理', path: '/imagetext', componentPath: '../../views/imagetext/imagetextList.vue', componentFound: true}
user.js?t=1750768869046:119 成功添加路由: /imagetext
user.js?t=1750768869046:150 构建的菜单树: (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
user.js?t=1750768869046:164 onLoad 执行完成，logined: true
user.js?t=1750768869046:228 用户信息加载成功，继续导航
user.js?t=1750768869046:306 应用启动检查: {hasToken: true, hasPermissions: true, logined: true}
user.js?t=1750768869046:310 应用启动时恢复动态路由...
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '小区信息', path: '/community', componentPath: '../../views/community/communityList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /community
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '组织管理', path: '/org', componentPath: '../../views/system/organizational.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /org
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '微信用户', path: '/member', componentPath: '../../views/system/miniUserList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /member
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '好物列表', path: '/goodsList', componentPath: '../../views/goods/list.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /goodsList
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '订单管理', path: '/orderList', componentPath: '../../views/goods/orderList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /orderList
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '定时任务', path: '/job', componentPath: '../../views/job/list.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /job
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '部门管理', path: '/dept', componentPath: '../../views/system/deptList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /dept
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '员工管理', path: '/person', componentPath: '../../views/system/personList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /person
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '职位管理', path: '/position', componentPath: '../../views/system/positionList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /position
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '小区访客', path: '/visiror', componentPath: '../../views/community/visitorList.vue', componentFound: false}
user.js?t=1750768869046:329 应用启动: 组件未找到: ../../views/community/visitorList.vue
（匿名） @ user.js?t=1750768869046:329
setTimeout
（匿名） @ user.js?t=1750768869046:302
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '小区工单', path: '/workorder', componentPath: '../../views/community/workorderList.vue', componentFound: false}
user.js?t=1750768869046:329 应用启动: 组件未找到: ../../views/community/workorderList.vue
（匿名） @ user.js?t=1750768869046:329
setTimeout
（匿名） @ user.js?t=1750768869046:302
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '用户管理', path: '/user', componentPath: '../../views/system/userList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /user
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '小区楼房', path: '/building', componentPath: '../../views/community/communityBuildingList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /building
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '角色管理', path: '/role', componentPath: '../../views/system/roleList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /role
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '好物管理', path: '/goods', componentPath: '../../views/goods/list.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /goods
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '小区住户', path: '/resident', componentPath: '../../views/community/communityResidentList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /resident
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '缴费项目', path: '/paymentItemsList', componentPath: '../../views/property/paymentItemsList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /paymentItemsList
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '菜单管理', path: '/menu', componentPath: '../../views/system/menuList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /menu
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '小区车辆', path: '/vehicleList', componentPath: '../../views/community/communityVehicleList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /vehicleList
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '物业账单', path: '/propertyBillList', componentPath: '../../views/property/billList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /propertyBillList
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '数据字典', path: '/dict', componentPath: '../../views/system/dictList.vue', componentFound: true}componentFound: truecomponentPath: "../../views/system/dictList.vue"menuName: "数据字典"path: "/dict"[[Prototype]]: Object
user.js?t=1750768869046:327 应用启动成功添加路由: /dict
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '小区管理', path: '/community', componentPath: '../../views/community/list.vue', componentFound: false}
user.js?t=1750768869046:329 应用启动: 组件未找到: ../../views/community/list.vue
（匿名） @ user.js?t=1750768869046:329
setTimeout
（匿名） @ user.js?t=1750768869046:302
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '活动管理', path: '/activity', componentPath: '../../views/activity/list.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /activity
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '访客管理', path: '/visitor', componentPath: '../../views/visitor/list.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /visitor
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '消息通知', path: '/noticeList', componentPath: '../../views/notice/noticeList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /noticeList
user.js?t=1750768869046:316 应用启动恢复路由: {menuName: '图文管理', path: '/imagetext', componentPath: '../../views/imagetext/imagetextList.vue', componentFound: true}
user.js?t=1750768869046:327 应用启动成功添加路由: /imagetext
user.js?t=1750768869046:360 应用启动时恢复登录状态和菜单树