/**
 * 全局小区存储 - 简化版本
 * 类似微信小程序的存储模式
 */

// 全局状态
let changeListeners = []

// 获取 Pinia store 实例
const getStore = () => window.$local

// 初始化时从 Pinia 加载数据
const initializeFromStore = () => {
  const store = getStore()
  if (!store) return

  // 如果 Pinia 中有数据，触发监听器
  const selectedCommunity = store.get('selectedCommunity')
  if (selectedCommunity && changeListeners.length > 0) {
    changeListeners.forEach(listener => {
      try {
        listener(selectedCommunity)
      } catch (error) {
        console.error('小区变化监听器执行失败:', error)
      }
    })
  }
}

// ==================== 核心API方法 ====================

/**
 * 获取当前选中的小区ID - 主要API
 * @returns {number|null} 小区ID
 */
export const getSelectedCommunityId = () => {
  const store = getStore()
  const selectedCommunity = store?.get('selectedCommunity')
  return selectedCommunity?.id || null
}

/**
 * 获取当前选中的小区对象
 * @returns {Object|null} 小区对象
 */
export const getSelectedCommunity = () => {
  const store = getStore()
  return store?.get('selectedCommunity') || null
}

/**
 * 设置选中的小区
 * @param {Object|null} community - 小区对象
 */
export const setSelectedCommunity = (community) => {
  const store = getStore()
  if (store) {
    store.setSelectedCommunity(community)
  }

  // 通知所有监听器
  changeListeners.forEach(listener => {
    try {
      listener(community)
    } catch (error) {
      console.error('小区变化监听器执行失败:', error)
    }
  })
}

/**
 * 设置小区列表
 * @param {Array} list - 小区列表
 */
export const setCommunityList = (list) => {
  const store = getStore()
  if (store) {
    store.setCommunityList(list || [])
  }

  // 如果有小区数据且没有选中的小区，默认选择第一个
  const communityList = list || []
  const selectedCommunity = store?.get('selectedCommunity')
  if (communityList.length > 0 && !selectedCommunity) {
    setSelectedCommunity(communityList[0])
  }
}

/**
 * 获取小区列表
 * @returns {Array} 小区列表
 */
export const getCommunityList = () => {
  const store = getStore()
  return store?.get('communityList') || []
}

/**
 * 检查是否有选中的小区
 * @returns {boolean} 是否有选中的小区
 */
export const hasSelectedCommunity = () => {
  const store = getStore()
  const selectedCommunity = store?.get('selectedCommunity')
  return !!selectedCommunity
}

/**
 * 添加小区变化监听器
 * @param {Function} listener - 监听器函数
 */
export const addCommunityChangeListener = (listener) => {
  if (typeof listener === 'function') {
    changeListeners.push(listener)
  }
}

/**
 * 移除小区变化监听器
 * @param {Function} listener - 监听器函数
 */
export const removeCommunityChangeListener = (listener) => {
  const index = changeListeners.indexOf(listener)
  if (index > -1) {
    changeListeners.splice(index, 1)
  }
}

/**
 * 清空所有数据
 */
export const clearAllData = () => {
  const store = getStore()
  if (store) {
    store.setSelectedCommunity(null)
    store.setCommunityList([])
  }
  changeListeners = []
}

// ==================== 兼容性导出 ====================
export default {
  getSelectedCommunity,
  getSelectedCommunityId,
  setSelectedCommunity,
  setCommunityList,
  getCommunityList,
  hasSelectedCommunity,
  addCommunityChangeListener,
  removeCommunityChangeListener,
  clearAllData
}
