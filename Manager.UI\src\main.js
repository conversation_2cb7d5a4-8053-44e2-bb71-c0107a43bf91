import { createApp } from 'vue'
import App from './App.vue'

import router from '@/router/index'
import store from '@/store/index'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 导入全局样式
import '@/assets/styles/global.css'

// 导入小区变化监听全局混入
import communityMixin from '@/mixins/communityMixin.js'

import { Base64 } from 'js-base64';

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { useStore } from "@/store/lcAssign";
const app = createApp(App)


const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
 // 这里要把store传进去
app.use(pinia)
const lcStore = useStore();

// 创建全局存储工具
const localStorageUtil = {
  // 设置方法
  set: (key, value) => {
    if (key === 'smartPropertyUserInfo') {
      lcStore.setSmartPropertyUserInfo(value)
    } else if (key === 'smartPropertyToken') {
      lcStore.setSmartPropertyToken(value)
    } else if (key === 'frontPermissions') {
      lcStore.setFrontPermissions(value)
    } else if (key === 'dictList') {
      lcStore.setDictList(value)
    } else {
      // 其他数据直接存储到 store 的 state 中
      lcStore[key] = value
    }
  },

  // 获取方法
  get: (key) => {
    if (key === 'smartPropertyUserInfo') {
      return lcStore.smartPropertyUserInfo
    } else if (key === 'smartPropertyToken') {
      return lcStore.smartPropertyToken
    } else if (key === 'frontPermissions') {
      return lcStore.frontPermissions
    } else if (key === 'dictList') {
      return lcStore.dictList
    } else {
      return lcStore[key]
    }
  },

  // 移除方法
  remove: (key) => {
    if (key === 'smartPropertyUserInfo') {
      lcStore.setSmartPropertyUserInfo(null)
    } else if (key === 'smartPropertyToken') {
      lcStore.setSmartPropertyToken(null)
    } else if (key === 'frontPermissions') {
      lcStore.setFrontPermissions(null)
    } else if (key === 'dictList') {
      lcStore.setDictList(null)
    } else {
      lcStore[key] = null
    }
  },

  // 清除所有
  clear: () => {
    lcStore.removeAll()
  },

  // 直接访问 store
  store: lcStore
}

// 全局注册小区变化监听混入
app.mixin(communityMixin)

// 设置全局配置，关闭警告信息
app.config.warnHandler = () => {};

// 挂载到全局
app.config.globalProperties.$local = localStorageUtil
window.$local = localStorageUtil

// 保持原有的 $lcStore 兼容性
app.config.globalProperties.$lcStore = lcStore
window.$lcStore = lcStore

app.use(router)
app.use(store)
app.use(ElementPlus)

app.mount('#app')
