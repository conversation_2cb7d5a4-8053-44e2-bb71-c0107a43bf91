import { createApp } from 'vue'
import App from './App.vue'

import router from '@/router/index'
import store from '@/store/index'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 导入全局样式
import '@/assets/styles/global.css'

// 导入小区变化监听全局混入
import communityMixin from '@/mixins/communityMixin.js'

import { Base64 } from 'js-base64';

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { useStore } from "@/store/lcAssign";
const app = createApp(App)


const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
 // 这里要把store传进去
app.use(pinia)
const lcStore = useStore();

// 全局注册小区变化监听混入
app.mixin(communityMixin)

// 设置全局配置，关闭警告信息
app.config.warnHandler = () => {};
app.config.globalProperties.$Base64 = Base64
// 直接挂载 store 到全局
app.config.globalProperties.$local = lcStore
window.$local = lcStore

// 保持原有的 $lcStore 兼容性
app.config.globalProperties.$lcStore = lcStore
window.$lcStore = lcStore

app.use(router)
app.use(store)
app.use(ElementPlus)

app.mount('#app')
