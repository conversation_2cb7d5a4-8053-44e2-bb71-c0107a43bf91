
// store.js
import { defineStore } from "pinia";

export const useStore = defineStore('piniaStore', {
  state: () => {
    return {
      smartPropertyUserInfo: null,
      smartPropertyToken: null,
      frontPermissions: null,
      dictList: null,
 
      
    }
  },

  /* 
    添加getters，接收一个对象，该对象里面是各种方法，
    你可以把它想象成Vue中的计算属性，它的作用就是返回一个结果，
    getters也是会被缓存的，
    在getters中的，各个方法之间相互调用采用this关键字即可
  */
  getters: {
    // changeName: (state) => (state.userInfo.changeName = state.userInfo.nickName + "逆天而行"),

  },

  /*
    添加actions，属性值是一个对象，该对象里面是各种方法，包括同步、异步方法
    特殊之处：actions中的方法内部的this指向的是当前store，即：
    在`defineStore`中，state对象中的属性，会被绑定到this上，可以通过this.name来访问和修改name属性，
    这里state对象中定义了name、age、sex属性，因此可以通过this.name、this.age、this.sex 来访问和修改这些属性
  */
  actions: {


    removeAll() {
      this.smartPropertyUserInfo = null
this.smartPropertyToken = null
      
      this.frontPermissions = null
      this.dictList = null
   
    },

    // 可以包含异步操作
    saveUserInfo(target, info) {

      if (info) {
        var base64UserInfo1 = target.encode(JSON.stringify(info))
        var base64UserInfo2 = target.encode(base64UserInfo1 + 'propertyproaW4S2r6E1DQK5fPWzBA5')

        this.smartPropertyUserInfo = base64UserInfo2
      } else {
        this.smartPropertyUserInfo = info
      }

    },

    getUserInfo(target) {

      if (this.smartPropertyUserInfo == null)
        return null

      var u2 = target.decode(this.smartPropertyUserInfo)
      var u1str = target.decode(u2.split('propertypro')[0])

      return JSON.parse(u1str);
    },

    setSmartPropertyUserInfo(smartPropertyUserInfo) {
      this.smartPropertyUserInfo = smartPropertyUserInfo
    },

    setSmartPropertyToken(smartPropertyToken) {
      this.smartPropertyToken = smartPropertyToken
    },

    setFrontPermissions(frontPermissions) {
      this.frontPermissions = frontPermissions
    },

    setDictList(dictList) {
      this.dictList = null
      this.dictList = dictList
    },


  },


  persist: {
    // 持久化存储
    paths: [ 'smartPropertyUserInfo','smartPropertyToken',  'frontPermissions', 'dictList',"popUpListUsedToEvents", 'wdcyToken', 'wdcySocketToken', 'dictList']
  },

})


