<template>
	<div id="app">
		<router-view></router-view>
	</div>
</template>

<script>
export default {
	mounted() {
		// 确保主题正确初始化
		const currentTheme = window.$local?.get('theme') || 'light';
		console.log('初始化主题:', currentTheme);

		// 应用主题到 DOM
		this.applyTheme(currentTheme);

		// 如果有 store 主题模块，也调用它的初始化
		if (this.$store && this.$store.theme && this.$store.theme.applyTheme) {
			this.$store.theme.applyTheme();
		}

		// 监听主题变化
		this.$watch(
			() => window.$local?.get('theme') || 'light',
			(newTheme) => {
				// 主题变化时更新
				console.log('主题已更改为:', newTheme);
				this.applyTheme(newTheme);
			}
		);
	},

	methods: {
		/**
		 * 应用主题到 DOM
		 */
		applyTheme(theme) {
			document.documentElement.classList.remove('dark-theme');
			document.body.classList.remove('dark-theme');

			if (theme === 'dark') {
				document.documentElement.classList.add('dark-theme');
				document.body.classList.add('dark-theme');
			}
		}
	}
}
</script>

<style>
	html,
	body,
	#app {
		width: 100vw;
		height: 100vh;
		margin: 0px;
		padding: 0px;
		transition: background-color var(--transition-duration), color var(--transition-duration);
	}
</style>