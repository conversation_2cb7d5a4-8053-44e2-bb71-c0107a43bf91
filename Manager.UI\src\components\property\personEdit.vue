<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="800px" class="person-edit-dialog">
    <el-form :model="personModel" :rules="rules" ref="formRef" label-width="100px" class="person-edit-form">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="姓名" prop="personName">
            <el-input v-model="personModel.personName" maxlength="50" placeholder="请输入人员姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="personModel.gender" placeholder="请选择性别" style="width: 100%;">
              <el-option label="男" value="male" />
              <el-option label="女" value="female" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件类型" prop="certificateType">
            <el-select v-model="personModel.certificateType" placeholder="请选择证件类型" style="width: 100%;">
              <el-option label="身份证" value="id_card" />
              <el-option label="护照" value="passport" />
              <el-option label="军官证" value="military_id" />
              <el-option label="驾驶证" value="driver_license" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="idCard">
            <el-input v-model="personModel.idCard" maxlength="20" placeholder="请输入身份证号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生日" prop="birthday">
            <el-date-picker v-model="personModel.birthday" type="date" placeholder="请选择生日"
              style="width: 100%;" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="personModel.phone" maxlength="11" placeholder="请输入手机号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="personModel.email" maxlength="100" placeholder="请输入邮箱" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应急手机号" prop="emerPhone">
            <el-input v-model="personModel.emerPhone" maxlength="11" placeholder="请输入应急手机号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="住址" prop="address">
            <el-input v-model="personModel.address" maxlength="200" placeholder="请输入住址" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="人员编号" prop="number">
            <el-input-number v-model="personModel.number" :min="1" style="width: 100%;" placeholder="请输入人员编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职位" prop="duty">
            <el-input v-model="personModel.duty" maxlength="50" placeholder="请输入职位" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入职时间" prop="entryTime">
            <el-date-picker v-model="personModel.entryTime" type="date" placeholder="请选择入职时间"
              style="width: 100%;" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="personModel.status" placeholder="请选择状态" style="width: 100%;">
              <el-option label="在职" value="active" />
              <el-option label="离职" value="inactive" />
              <el-option label="试用" value="trial" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="薪资" prop="salary">
            <el-input-number v-model="personModel.salary" :min="0" :precision="2" style="width: 100%;" placeholder="请输入薪资" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历" prop="qualification">
            <el-select v-model="personModel.qualification" placeholder="请选择学历" style="width: 100%;">
              <el-option label="小学" value="primary" />
              <el-option label="初中" value="junior" />
              <el-option label="高中" value="senior" />
              <el-option label="大专" value="college" />
              <el-option label="本科" value="bachelor" />
              <el-option label="硕士" value="master" />
              <el-option label="博士" value="doctor" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专业" prop="major">
            <el-input v-model="personModel.major" maxlength="50" placeholder="请输入专业" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="绩效" prop="performance">
            <el-input v-model="personModel.performance" maxlength="100" placeholder="请输入绩效" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="技能" prop="skills">
            <el-input v-model="personModel.skills" maxlength="200" placeholder="请输入技能，多个用逗号分隔" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="证书" prop="certificates">
            <el-input v-model="personModel.certificates" maxlength="200" placeholder="请输入证书，多个用逗号分隔" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="照片" prop="media">
            <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              :action="uploadUrl"
              :headers="headers"
              :on-success="uploadSuccess"
              :before-upload="beforeUpload"
              list-type="picture-card">
              <img v-if="imageShow" :src="imageUrl" style="width: 100%;height: 100%;" />
              <el-button v-else size="small" type="primary">点击上传</el-button>
            </el-upload>
            <div class="upload-tip">
              支持jpg、png、gif格式，文件大小不超过2MB
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input v-model="personModel.note" type="textarea" :rows="3" maxlength="500"
              placeholder="请输入备注" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { addPropertyPerson, editPropertyPerson } from '@/api/property/person'
import mitt from '@/utils/mitt'

export default {
  name: 'personEdit',
  data() {
    return {
      uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      headers: {
        'Authorization': JSON.parse(localStorage.getItem("smart_property_token"+localStorage.getItem("userName"))).access_token
      },
      personModel: {
        id: null,
        personName: '',
        gender: '',
        certificateType: '',
        idCard: '',
        birthday: '',
        email: '',
        address: '',
        phone: '',
        emerPhone: '',
        number: null,
        duty: '',
        entryTime: '',
        status: '',
        salary: null,
        performance: '',
        qualification: '',
        major: '',
        skills: '',
        certificates: '',
        media: '',
        note: ''
      },
      imageShow: false,
      imageUrl: '',
      loading: false,
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        personName: [
          { required: true, message: '请输入人员姓名', trigger: 'blur' },
          { min: 2, max: 50, message: '姓名长度在2到50个字符', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        emerPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的应急手机号', trigger: 'blur' }
        ],
        idCard: [
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        duty: [
          { required: true, message: '请输入职位', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    /**
     * 上传前验证
     */
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    /**
     * 上传成功处理
     */
    uploadSuccess(res) {
      if (res.code == 0) {
        this.imageShow = true
        this.imageUrl = this.imgServer + res.data
        // 使用新的数据格式：直接存储文件路径
        this.personModel.media = res.data
      } else {
        this.$message.error(res.message)
      }
    },

    /**
     * 提交表单
     */
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return

        this.loading = true
        const api = this.personModel.id ? editPropertyPerson : addPropertyPerson

        // 处理数据格式
        const submitData = { ...this.personModel }

        // 确保数字类型字段正确
        if (submitData.number) {
          submitData.number = Number(submitData.number)
        }
        if (submitData.salary) {
          submitData.salary = Number(submitData.salary)
        }

        api(submitData).then(() => {
          this.$emit('search')
          this.dialog.show = false
          this.$message.success('操作成功')
          this.resetForm()
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '操作失败')
        }).finally(() => {
          this.loading = false
        })
      })
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.personModel = {
        id: null,
        personName: '',
        gender: '',
        certificateType: '',
        idCard: '',
        birthday: '',
        email: '',
        address: '',
        phone: '',
        emerPhone: '',
        number: null,
        duty: '',
        entryTime: '',
        status: '',
        salary: null,
        performance: '',
        qualification: '',
        major: '',
        skills: '',
        certificates: '',
        media: '',
        note: ''
      }
      this.imageShow = false
      this.imageUrl = ''

      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate()
        }
      })
    }
  },
  mounted() {
    mitt.on('openPersonEdit', (data) => {
      // 处理媒体文件显示
      if (data.media) {
        try {
          // 尝试解析旧格式（JSON对象）
          if (data.media.startsWith('{')) {
            const mediaObj = JSON.parse(data.media)
            if (mediaObj.face_url) {
              this.imageShow = true
              this.imageUrl = this.imgServer + mediaObj.face_url
            }
          } else {
            // 新格式（直接是文件路径）
            this.imageShow = true
            this.imageUrl = this.imgServer + data.media
          }
        } catch (e) {
          // 如果解析失败，尝试直接作为路径使用
          if (data.media) {
            this.imageShow = true
            this.imageUrl = this.imgServer + data.media
          }
        }
      }

      this.personModel = { ...data }
      this.dialog.show = true
      this.dialog.title = '编辑物业人员'
    })

    mitt.on('openPersonAdd', () => {
      this.resetForm()
      this.dialog.show = true
      this.dialog.title = '新增物业人员'
    })
  },
  beforeDestroy() {
    mitt.off('openPersonEdit')
    mitt.off('openPersonAdd')
  }
}
</script>

<style scoped>
.person-edit-dialog>>>.el-dialog__body {
  padding-top: 10px;
  padding-bottom: 0;
}

.person-edit-form {
  padding: 0 10px;
}

.avatar-uploader {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 60px;
}

.avatar-uploader .avatar {
  width: 120px;
  height: 80px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  line-height: 1.4;
}
</style>