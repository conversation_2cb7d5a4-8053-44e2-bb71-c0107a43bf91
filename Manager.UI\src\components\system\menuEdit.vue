<template>
	<el-dialog width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="menuModel" label-width="120px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="菜单名称" prop="menuName">
						<el-input v-model="menuModel.menuName" placeholder="菜单名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="图标" prop="icon">
						<el-select v-model="menuModel.icon" placeholder="请选择图标" style="width: 100%" clearable filterable>
							<el-option v-for="icon in iconOptions" :key="icon.value" :value="icon.value">
								<template #default>
									<el-icon style="margin-right: 8px;"><component :is="icon.component" /></el-icon>
									<span>{{ icon.label }}</span>
								</template>
							</el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="排序" prop="sort">
						<el-input v-model="menuModel.sort" placeholder="排序"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="路径名" prop="path">
						<el-input v-model="menuModel.path" placeholder="路径名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="权限" prop="permission">
						<el-input v-model="menuModel.permission" placeholder="权限"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="组件路径" prop="componentPath">
						<el-input v-model="menuModel.componentPath" placeholder="组件路径"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="类型" prop="menuType">
						<el-select style="width: 100%;" v-model="menuModel.menuType" clearable placeholder="类型">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="上级" prop="parentId">
						<el-select v-model="menuModel.parentId" placeholder="请选择上级菜单" style="width: 100%" clearable filterable>
							<el-option label="顶级菜单" :value="0"></el-option>
							<el-option
								v-for="menu in parentMenuOptions"
								:key="menu.id"
								:label="menu.menuName"
								:value="menu.id"
								:disabled="menu.id === menuModel.id"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" :rows="2" v-model="menuModel.remark" placeholder="备注内容"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提
				交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { addMenu, editMenu, loadMenu } from "@/api/system/menu";
import mitt from "@/utils/mitt";
import {
	House, User, Setting, Document, DataLine,
	Monitor, Tools, Bell, Message, Calendar,
	Folder, Files, Grid, List, Search,
	Plus, Edit, Delete, View, Download
} from '@element-plus/icons-vue';
export default {
	components: {
		House, User, Setting, Document, DataLine,
		Monitor, Tools, Bell, Message, Calendar,
		Folder, Files, Grid, List, Search,
		Plus, Edit, Delete, View, Download
	},
	props: ['statusList', 'typeList'],
	data() {
		return {
			loading: false,
			menuModel: {},
			dialog: {},
			parentMenuOptions: [], // 上级菜单选项
			iconOptions: [ // 图标选项
				{ label: '首页', value: 'House', component: House },
				{ label: '用户', value: 'User', component: User },
				{ label: '设置', value: 'Setting', component: Setting },
				{ label: '文档', value: 'Document', component: Document },
				{ label: '数据', value: 'DataLine', component: DataLine },
				{ label: '监控', value: 'Monitor', component: Monitor },
				{ label: '工具', value: 'Tools', component: Tools },
				{ label: '通知', value: 'Bell', component: Bell },
				{ label: '消息', value: 'Message', component: Message },
				{ label: '日历', value: 'Calendar', component: Calendar },
				{ label: '文件夹', value: 'Folder', component: Folder },
				{ label: '文件', value: 'Files', component: Files },
				{ label: '网格', value: 'Grid', component: Grid },
				{ label: '列表', value: 'List', component: List },
				{ label: '搜索', value: 'Search', component: Search },
				{ label: '添加', value: 'Plus', component: Plus },
				{ label: '编辑', value: 'Edit', component: Edit },
				{ label: '删除', value: 'Delete', component: Delete },
				{ label: '查看', value: 'View', component: View },
				{ label: '下载', value: 'Download', component: Download }
			],
			rules: {
				menuName: [{
					required: true,
					message: '请输入菜单名',
					trigger: 'blur',
				}],
				type: [{
					required: true,
					message: '请选择类型',
					trigger: 'change',
				}]
			}
		}
	},
	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.menuModel.id == 0) {
						addMenu(this.menuModel)
							.then(res => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							})
							.catch(err => {
								this.$message.error(err.data.errorMessage);
							})
					} else {
						editMenu(this.menuModel)
							.then(res => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							})
							.catch(err => {
								this.$message.error(err.data.errorMessage);
							})
					}

				}
			})
		},

		/**
		 * 加载菜单列表用于上级选择
		 */
		loadMenuList() {
			loadMenu()
				.then(res => {
					// 筛选出menuType='menu'的菜单项
					const allMenus = res.data.data || []
					this.parentMenuOptions = allMenus.filter(menu => menu.menuType === 'menu')
				})
				.catch(err => {
					console.error('加载菜单列表失败:', err)
					this.parentMenuOptions = []
				})
		}
	},
	mounted() {
		// 加载菜单列表
		this.loadMenuList()
		mitt.on('openMenuEdit', (menu) => {
			this.menuModel = menu
			this.dialog.show = true
			this.dialog.title = "修改信息"
		})
		mitt.on('openMenuAdd', (id) => {
			this.menuModel = {
				id: 0,
				parentId: id
			}
			this.dialog.show = true
			this.dialog.title = "添加菜单"
		})
	}
}
</script>

<style scoped>
.menu-edit-form :deep(.el-form-item) {
	margin-bottom: 12px !important;
}
</style> 