<template>
	<el-dialog width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="menuModel" label-width="120px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="菜单名称" prop="menuName">
						<el-input v-model="menuModel.menuName" placeholder="菜单名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="图标" prop="icon">
						<el-input v-model="menuModel.icon" placeholder="类型"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="排序" prop="sort">
						<el-input v-model="menuModel.sort" placeholder="排序"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="路径名" prop="path">
						<el-input v-model="menuModel.path" placeholder="路径名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="权限" prop="permission">
						<el-input v-model="menuModel.permission" placeholder="权限"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="组件路径" prop="componentPath">
						<el-input v-model="menuModel.componentPath" placeholder="组件路径"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="类型" prop="menuType">
						<el-select style="width: 100%;" v-model="menuModel.menuType" clearable placeholder="类型">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="上级" prop="parentId">
						<el-input v-model="menuModel.parentId" placeholder="上级"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" :rows="2" v-model="menuModel.remark" placeholder="备注内容"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提
				交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { addMenu, editMenu } from "@/api/system/menu";
import mitt from "@/utils/mitt";
export default {
	props: ['statusList', 'typeList'],
	data() {
		return {
			loading: false,
			menuModel: {},
			dialog: {},
			rules: {
				menuName: [{
					required: true,
					message: '请输入菜单名',
					trigger: 'blur',
				}],
				type: [{
					required: true,
					message: '请选择类型',
					trigger: 'change',
				}]
			}
		}
	},
	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.menuModel.id == 0) {
						addMenu(this.menuModel)
							.then(res => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							})
							.catch(err => {
								this.$message.error(err.data.errorMessage);
							})
					} else {
						editMenu(this.menuModel)
							.then(res => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							})
							.catch(err => {
								this.$message.error(err.data.errorMessage);
							})
					}

				}
			})
		}
	},
	mounted() {
		mitt.on('openMenuEdit', (menu) => {
			this.menuModel = menu
			this.dialog.show = true
			this.dialog.title = "修改信息"
		})
		mitt.on('openMenuAdd', (id) => {
			this.menuModel = {
				id: 0,
				parentId: id
			}
			this.dialog.show = true
			this.dialog.title = "添加菜单"
		})
	}
}
</script>

<style scoped>
.menu-edit-form :deep(.el-form-item) {
	margin-bottom: 12px !important;
}
</style> 