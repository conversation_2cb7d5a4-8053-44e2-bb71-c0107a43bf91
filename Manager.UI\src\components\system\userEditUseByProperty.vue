<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="800px" destroy-on-close @close="handleClose"
    class="user-edit-dialog">

    <el-form :model="userModel" :rules="rules" ref="formRef" label-width="120px" class="user-edit-form">

      <!-- 用户信息表单 -->
      <div class="form-section">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="userName">
              <el-input v-model="userModel.userName" placeholder="请输入用户名" clearable maxlength="50">
              </el-input>
            </el-form-item>
          </el-col>
             <el-col :span="12" v-if=" userModel.id?false:true ">
            <el-form-item label="用户密码" prop="password">
              <el-input v-model="userModel.password" placeholder="请输入该用户密码" clearable maxlength="50">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickName">
              <el-input v-model="userModel.nickName" placeholder="请输入昵称" clearable maxlength="50">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userModel.email" placeholder="请输入邮箱地址" clearable maxlength="100">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="userModel.phone" placeholder="请输入手机号" clearable maxlength="11">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户角色" prop="roleId">
              <el-select v-model="userModel.roleId" placeholder="请选择角色" style="width: 100%;" clearable>
                <el-option v-for="role in roleList" :key="role.id" :label="role.roleName" :value="role.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="userModel.status" placeholder="请选择状态" style="width: 100%;" clearable>
                <el-option v-for="role in userStatusList" :key="role.nameEn" :label="role.nameCn" :value="role.nameEn">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 组织角色信息 -->
        <el-row :gutter="20" v-if="!isAssigningToEmployee">
          <el-col :span="12">
            <el-form-item label="所属组织" prop="orgId">
              <el-cascader v-model="userModel.orgId" :options="orgTreeData" :props="orgProps" placeholder="请选择组织"
                style="width: 100%;" clearable filterable :show-all-levels="false">
              </el-cascader>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 头像上传 - 仅在非员工分配账号时显示 -->
        <el-row :gutter="20" v-if="!isAssigningToEmployee">
          <el-col :span="12">
            <el-form-item label="头像上传" prop="avatarUrl">
              <el-upload class="avatar-uploader" :action="uploadUrl" :show-file-list="false"
                :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :headers="uploadHeaders">
                <img v-if="userModel.avatarUrl" :src="userModel.avatarUrl" class="avatar-upload-img">
                <el-icon v-else class="avatar-uploader-icon">
                  <Plus />
                </el-icon>
              </el-upload>
              <div class="upload-tip">支持 jpg、png 格式，文件大小不超过 2MB</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <div class="avatar-preview" v-if="userModel.avatarUrl">
              <img :src="userModel.avatarUrl" alt="头像预览" class="avatar-img" />
            </div>
          </el-col>
        </el-row>

        <!-- 备注信息 - 仅在非员工分配账号时显示 -->
        <el-row v-if="!isAssigningToEmployee">
          <el-col :span="24">
            <el-form-item label="备注" prop="note">
              <el-input v-model="userModel.note" type="textarea" placeholder="请输入备注信息" :rows="3" maxlength="500"
                show-word-limit clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialog.show = false">取 消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { addUser, editUser } from '@/api/system/user'
import { getOrgTree } from '@/api/system/org'
import { getDeptTree } from '@/api/system/dept'
import { listRole } from '@/api/system/role'
import { Plus } from '@element-plus/icons-vue'
import mitt from '@/utils/mitt'

export default {
  name: 'UserEdit',
  components: {
    Plus
  },
  props: ['userStatusList'],
  data() {
    return {
      uploadUrl: import.meta.env.VITE_BASE_API + "/common-api/v1/file/upload",
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      loading: false,
      userModel: {
        id: null,
        userName: '',
        nickName: '',
        email: '',
        password: '',
        phone: '',
        avatarUrl: '',
        orgId: null,
        roleId: null,
        note: '',
        personId: null // 添加员工ID字段
      },
      dialog: {
        show: false,
        title: ''
      },
      orgTreeData: [],
      deptTreeData: [],
      roleList: [],
      uploadHeaders: {
        'Authorization': ''
      },
      orgProps: {
        value: 'id',
        label: 'orgName',
        children: 'children',
        emitPath: false,
        checkStrictly: true,
        expandTrigger: 'click'
      },
      deptProps: {
        value: 'id',
        label: 'deptName',
        children: 'children',
        emitPath: false,
        checkStrictly: true,
        expandTrigger: 'click'
      },
      rules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
        ],
          password: [
          { required: true, message: '请输入用户密码', trigger: 'blur' },
          { min: 8, max: 50, message: '用户密码长度在 8 到 50 个字符', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '请输入昵称', trigger: 'blur' },
          { min: 1, max: 5, message: '昵称长度在 1 到 5 个字符', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        orgId: [
          {
            required: true,
            message: '请选择所属组织',
            trigger: 'change',
            validator: (rule, value, callback) => {
              // 如果是为员工分配账号，则不需要验证组织字段
              if (this.isAssigningToEmployee) {
                callback();
              } else if (!value) {
                callback(new Error('请选择所属组织'));
              } else {
                callback();
              }
            }
          }
        ],
        roleId: [
          { required: true, message: '请选择用户角色', trigger: 'change' }
        ]
      }
    }
  },

  computed: {
    uploadUrl() {
      return this.uploadUrl;
    },
    // 判断是否是为员工分配账号
    isAssigningToEmployee() {
      return !!this.userModel.personId;
    }
  },

  methods: {
    /**
     * 提交表单
     */
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;

        this.loading = true;

        const apiCall = this.userModel.id ? editUser(this.userModel) : addUser(this.userModel);

        apiCall
          .then(() => {
            this.$message.success(this.userModel.id ? '修改成功' : '添加成功');
            this.dialog.show = false;
            this.$emit('search');
          })
          .catch(err => {
            this.$message.error(err.data?.errorMessage || '操作失败');
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },

    /**
     * 组织选择变化事件
     */
    onOrgChange(value) {
      console.log('选择的组织ID:', value);
    },

    /**
     * 头像上传成功回调
     */
    handleAvatarSuccess(response) {
      console.log('头像上传成功:', response);
      if (response && response.data) {
        this.userModel.avatarUrl = this.imgServer + response.data;
        this.$message.success('头像上传成功');
      } else {
        this.$message.error('头像上传失败，请重试');
      }
    },

    /**
     * 头像上传前的校验
     */
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('头像图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },

    /**
     * 关闭弹窗事件
     */
    handleClose() {
      this.resetForm();
    },

    /**
     * 重置表单
     */
    resetForm() {
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
      this.userModel = {
        id: null,
        userName: '',
        nickName: '',
        email: '',
        password: '',
        phone: '',
        avatarUrl: '',
        orgId: null,
        roleId: null,
        note: '',
        personId: null
      };
    },

    /**
     * 将平铺的组织数据转换为树形结构
     */
    buildOrgTree(orgList) {
      if (!Array.isArray(orgList) || orgList.length === 0) {
        return [];
      }

      // 创建一个映射表
      const orgMap = {};
      const rootNodes = [];

      // 首先创建所有节点的映射
      orgList.forEach(org => {
        orgMap[org.id] = {
          id: org.id,
          orgName: org.orgName || org.name || `组织${org.id}`,
          parentId: org.parentId,
          children: []
        };
      });

      // 构建树形结构
      orgList.forEach(org => {
        const node = orgMap[org.id];
        if (org.parentId && orgMap[org.parentId]) {
          // 有父节点，添加到父节点的children中
          orgMap[org.parentId].children.push(node);
        } else {
          // 没有父节点或父节点不存在，作为根节点
          rootNodes.push(node);
        }
      });

      return rootNodes;
    },

    /**
     * 加载组织树数据
     */
    loadOrgTree() {
      getOrgTree()
        .then(res => {
          console.log('组织API响应:', res);
          
          try {
            this.orgTreeData = res.data.data.list;
          } catch (error) {
            console.error('加载组织树失败:', error);
            this.$message.error('加载组织数据失败');
            // 提供默认的组织结构
            this.orgTreeData = [];
          }

        })
        .catch(err => {
          console.error('加载组织树失败:', err);
          this.$message.error('加载组织数据失败');
          // 提供默认的组织结构
          this.orgTreeData = [];
        });
    },
    loadDeptTree() {
      getDeptTree()
        .then(res => {
          try {
            this.deptTreeData = res.data.data.list;
          } catch (error) {
            console.error('加载组织树失败:', error);
            this.$message.error('加载组织数据失败');
            // 提供默认的组织结构
            this.deptTreeData = [];
          }

        })
        .catch(err => {
          this.$message.error('加载部门数据失败');
          // 提供默认的组织结构
          this.deptTreeData = [];
        });
    },
    /**
     * 加载角色列表
     */
    loadRoleList() {
      listRole({ pageNum: 1, pageSize: 1000 })
        .then(res => {
          console.log('角色列表API响应:', res);
          // 处理不同的数据格式
          let roleData = [];
          if (res.data && res.data.data) {
            if (Array.isArray(res.data.data)) {
              roleData = res.data.data;
            } else if (res.data.data.list && Array.isArray(res.data.data.list)) {
              roleData = res.data.data.list;
            }
          }
          this.roleList = roleData;
          console.log('处理后的角色列表数据:', this.roleList);
        })
        .catch(err => {
          console.error('加载角色列表失败:', err);
          this.$message.error('加载角色数据失败');
          this.roleList = []; // 确保是数组
        });
    }
  },

  mounted() {
    console.log('UserEditNew 组件已挂载');

    // 设置上传请求头
    try {
      const token = localStorage.getItem("smart_property_token"+localStorage.getItem("userName"));
      if (token) {
        const authData = JSON.parse(token);
        if (authData && authData.access_token) {
          this.uploadHeaders.Authorization = authData.access_token;
        }
      }
    } catch (e) {
      console.error('设置上传请求头失败:', e);
    }

    // 加载基础数据
    this.loadOrgTree();
    this.loadDeptTree();
    this.loadRoleList();

    // 监听新增用户事件
    mitt.on('openUserAdd', (data) => {
      console.log('收到 openUserAdd 事件', data);
      this.resetForm();

      // 如果传入了员工数据，则预填充表单
      if (data) {
        this.userModel = {
          ...this.userModel,
          userName: data.userName || '',
          nickName: data.nickName || '',
          email: data.email || '',
          phone: data.phone || '',
          orgId: data.orgId || null,
          personId: data.personId || null
        };
      }

      this.dialog.title = data && data.personId ? '为员工分配账号' : '新增用户';
      this.dialog.show = true;
      console.log('弹窗状态已设置为显示:', this.dialog.show);
    });

    // 监听编辑用户事件
    mitt.on('openUserEdit', (data) => {
      console.log('收到 openUserEdit 事件，数据:', data);
      this.resetForm();
      this.userModel = {
        ...data,
        orgId: data.orgId || null,
        roleId: data.roleId || null,
        personId: data.personId || null
      };
      this.dialog.title = '编辑用户';
      this.dialog.show = true;
      console.log('弹窗状态已设置为显示:', this.dialog.show);
    });
  },

  beforeUnmount() {
    mitt.off('openUserAdd');
    mitt.off('openUserEdit');
  }
}
</script>

<style scoped>
.user-edit-dialog {
  border-radius: 8px;
}

.user-edit-form {
  padding: 0 16px;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 30px;
  height: 2px;
  background-color: #409eff;
}

.user-edit-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.user-edit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

/* 头像上传样式 */
.avatar-uploader {
  display: block;
}

.avatar-uploader :deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader :deep(.el-upload:hover) {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  text-align: center;
}

.avatar-upload-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  line-height: 1.4;
}

.avatar-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.avatar-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e8e8e8;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 12px;
}

/* 级联选择器样式优化 */
.user-edit-form :deep(.el-cascader) {
  width: 100%;
}

.user-edit-form :deep(.el-cascader__dropdown) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-edit-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 14px;
    margin-bottom: 12px;
  }
}

/* 暗色主题适配 */
.dark-theme .form-section {
  background-color: #2d3748;
  border-color: #4a5568;
}

.dark-theme .section-title {
  color: #e2e8f0;
}

.dark-theme .user-edit-form :deep(.el-form-item__label) {
  color: #e2e8f0;
}

.dark-theme .avatar-img {
  border-color: #4a5568;
}

/* 表单验证错误样式 */
.user-edit-form :deep(.el-form-item.is-error .el-input__inner),
.user-edit-form :deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: #f56c6c;
}

.user-edit-form :deep(.el-form-item__error) {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}



/* 选择器下拉样式 */
.user-edit-form :deep(.el-select-dropdown) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 密码输入框样式 */
.user-edit-form :deep(.el-input--password .el-input__suffix) {
  cursor: pointer;
}
</style>