import axios from 'axios'
import router from '@/router'

export var baseUrl = import.meta.env.VITE_BASE_API

// token刷新相关变量
let isRefreshing = false
let refreshSubscribers = []

// 创建axios实例
const service = axios.create({
    baseURL: baseUrl,
    timeout: 30000
})

// 获取token
function getToken() {
    debugger
    // 从 Pinia 获取 token
    const userName = window.$local?.get('userName')
    if (!userName) return null
    return window.$local?.get('smartPropertyToken')
}

// 判断token是否过期
function isTokenExpired(token) {
    try {
        let tokenObj = JSON.parse(token);
        // 检查token对象是否有效
        if (!tokenObj || !tokenObj.access_token) {
            console.log('Token格式无效或缺少access_token');
            return true;
        }

        // 如果是新登录的token（有access_token但没有expires_time），为其设置默认过期时间
        if (!tokenObj.expires_time) {
            console.log('检测到新登录的token，设置默认过期时间');
            // 设置默认2小时过期时间
            let defaultExpireTime = new Date().getTime() + (2 * 60 * 60 * 1000);
            tokenObj.expires_time = defaultExpireTime;
           
            // 更新 Pinia 中的 token
            window.$local?.setSmartPropertyToken(JSON.stringify(tokenObj));
            return false; // 新token不过期
        }

        let expires_time = tokenObj.expires_time;
        let currentTime = new Date().getTime();

        // 提前5分钟(300000毫秒)判断过期，避免即将过期的情况
        if (currentTime >= (expires_time - 300000)) {
            console.log('Token已过期或即将过期');
            return true;
        } else {
            return false;
        }
    } catch (error) {
        console.error('解析token时出错:', error);
        return true; // 解析出错时视为过期
    }
}

// 重新登录
function reLogin() {
    // 清除 Pinia 存储
    window.$local?.removeAll()
    router.replace({
        path: '/login'
    })
}

// 添加请求到刷新队列
function subscribeTokenRefresh(cb) {
    refreshSubscribers.push(cb)
}

// 执行刷新队列中的请求
function onRefreshed(token) {
    refreshSubscribers.map((cb) => cb(token))
    refreshSubscribers = []
}

// 刷新token
function refreshToken() {
    return new Promise((resolve, reject) => {
        try {
            let token = getToken();
            if (!token) {
                console.error('刷新token失败: token不存在');
                reLogin();
                return reject('token不存在');
            }

            let tokenObj = JSON.parse(token);
            if (!tokenObj || !tokenObj.refresh_token) {
                console.error('刷新token失败: refresh_token不存在');
                reLogin();
                return reject('refresh_token不存在');
            }


            axios({
                method: 'post',
                url: baseUrl + '/manage-api/v1/auth/refresh-token?refreshToken=' + tokenObj.refresh_token, // 刷新token的接口
                headers: {
                    'content-type': 'application/json'
                },
            })
                .then((res) => {
                    if (!res.data || res.data.code !== 0 || !res.data.data) {
                        console.error('刷新token失败: 接口返回错误', res.data);
                        reLogin();
                        return reject('刷新token接口返回错误');
                    }

                    try {
                        let expires_time = new Date().getTime() + parseInt(res.data.data.expires_in * 0.8) * 1000;
                        let currentToken = JSON.parse(getToken()) || {};

                        currentToken.expires_time = expires_time;
                        currentToken.access_token = res.data.data.access_token;
                        currentToken.refresh_token = res.data.data.refresh_token;

                        // 更新 Pinia 中的 token
                        window.$local?.setSmartPropertyToken(JSON.stringify(currentToken));

                        // 执行队列中的请求
                        onRefreshed(res.data.data.access_token);

                        resolve(res.data.data.access_token);
                    } catch (error) {
                        console.error('保存新token时出错:', error);
                        reLogin();
                        reject(error);
                    }
                })
                .catch((err) => {
                    console.error('刷新token请求失败:', err);
                    reLogin();
                    reject(err);
                });
        } catch (error) {
            console.error('刷新token过程中出错:', error);
            reLogin();
            reject(error);
        }
    });
}

// request拦截器
service.interceptors.request.use(
    (config) => {
        let token = getToken()

        if (token) {
            // 判断token是否过期,如果过期请求刷新token
            if (isTokenExpired(token)) {
                // 判断当前是否正在请求刷新token
                if (!isRefreshing) {
                    isRefreshing = true // 设置刷新状态，防止重复刷新

                    // 刷新token
                    return refreshToken()
                        .then((newToken) => {
                            isRefreshing = false
                            config.headers['Authorization'] = newToken
                            return config
                        })
                        .catch((error) => {
                            isRefreshing = false
                            return Promise.reject(error)
                        })
                } else {
                    // 如果正在刷新token，将请求加入队列
                    return new Promise((resolve) => {
                        subscribeTokenRefresh((newToken) => {
                            config.headers['Authorization'] = newToken
                            resolve(config)
                        })
                    })
                }
            } else {
                // token未过期，直接添加到请求头
                try {
                    const authData = JSON.parse(token);
                    if (authData && authData.access_token) {
                        config.headers['Authorization'] = authData.access_token;
                    }
                } catch (e) {
                    console.error('处理认证信息时出错:', e);
                }
                return config
            }
        } else {
            return config
        }
    },
    (error) => {
        return Promise.reject(error)
    }
)

// response拦截器
service.interceptors.response.use(
    async response => {
        let code = response.data.code
        if (code == 401 || code == 402) {
            console.log('接口返回401/402，token过期！')
            reLogin()
            return Promise.reject(response)
        } else if (code != 0) {
            return Promise.reject(response)
        } else {
            return response
        }
    },
    error => {
        // 处理HTTP状态码401/402
        if (error.response && (error.response.status === 401 || error.response.status === 402)) {
            console.log('请求返回401/402，token过期！')
            reLogin()
            return Promise.reject(error)
        }

        // 网络错误
        if (error && error.message === 'Network Error') {
            return Promise.reject("请求超时");
        }

        // 返回错误，确保错误能被正确捕获
        return Promise.reject(error);
    }
)

export default service
