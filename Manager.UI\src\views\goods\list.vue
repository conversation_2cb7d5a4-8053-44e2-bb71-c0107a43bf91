<template>
  <div class="goods-list-container">
    <div class="goods-list-content">
      <!-- 搜索区域 -->
      <div class="card card--search search-flex">
        <el-input
          v-model="searchModel.stuffDescribe"
          placeholder="商品描述"
          clearable
          style="width: 200px; margin-right: 16px;"
        />

        <el-select
          v-model="searchModel.examineStatus"
          placeholder="审核状态"
          clearable
          style="width: 120px; margin-right: 16px;"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in examineStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-model="searchModel.type"
          placeholder="好物类型"
          clearable
          style="width: 120px; margin-right: 16px;"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>

      <!-- 表格区域 -->
      <div class="card card--table">
        <div class="table-col">
          <el-table
            :data="goodsList"
            row-key="id"
            style="width: 100%; height: 100%;"
            class="data-table"
            v-loading="loading"
          >
            <el-table-column prop="id" label="ID" width="80" align="center"/>
            <el-table-column prop="stuffDescribe" label="商品标题" align="center" min-width="150" show-overflow-tooltip/>
            <el-table-column prop="media" label="商品图片" align="center" width="100">
              <template #default="scope">
                <el-image
                  v-if="scope.row.media"
                  :src="getFirstImageUrl(scope.row.media)"
                  style="width: 60px; height: 40px;"
                  fit="cover"
                  :preview-src-list="getImageUrlList(scope.row.media)"
               :preview-teleported="true"
                />
                <span v-else class="text-muted">无图片</span>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="价格" width="100" align="center">
              <template #default="scope">
                <span class="price-text">¥{{ scope.row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="好物类型" width="100" align="center">
              <template #default="scope">
                <span>{{ getTypeLabel(scope.row.type) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="categoryCode" label="分类" width="100" align="center">
              <template #default="scope">
                <span>{{ getCategoryLabel(scope.row.categoryCode) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="积分" width="80" align="center"/>
            <el-table-column prop="stock" label="库存" width="80" align="center"/>
            <el-table-column prop="views" label="浏览量" width="80" align="center"/>
            <el-table-column prop="examineStatus" label="审核状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getExamineStatusType(scope.row.examineStatus)" size="small">
                  {{ getExamineStatusLabel(scope.row.examineStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="stuffStatus" label="上架状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getStuffStatusType(scope.row.stuffStatus)" size="small">
                  {{ getStuffStatusLabel(scope.row.stuffStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="发布时间" width="160" align="center"/>
            <el-table-column label="操作" width="200" align="center">
              <template #default="scope">
                <el-button type="text" size="small" @click="viewDetail(scope.row)">查看</el-button>
                <el-dropdown
                  @command="(command) => auditGoods(scope.row, command)"
                  trigger="click"
                >
                  <el-button type="text" size="small">
                    状态管理<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <template v-for="status in examineStatusOptions" :key="status.value">
                        <el-dropdown-item
                          v-if="scope.row.examineStatus !== status.value"
                          :command="status.value"
                        >
                          <el-icon><clock /></el-icon>
                          {{ status.label }}
                        </el-dropdown-item>
                      </template>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-button type="text" size="small" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="currentChange"
            :current-page="searchModel.pageNum"
            :page-size="searchModel.pageSize"
            layout="total, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.title"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="detailDialog.data" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品ID">{{ detailDialog.data.id }}</el-descriptions-item>
          <el-descriptions-item label="商品描述">{{ detailDialog.data.stuffDescribe }}</el-descriptions-item>
          <el-descriptions-item label="价格">
            <span class="price-text">¥{{ detailDialog.data.amount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="积分">{{ detailDialog.data.points }}</el-descriptions-item>
          <el-descriptions-item label="好物类型">{{ getTypeLabel(detailDialog.data.type) }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ getCategoryLabel(detailDialog.data.categoryCode) }}</el-descriptions-item>
          <el-descriptions-item label="库存">{{ detailDialog.data.stock }}</el-descriptions-item>
          <el-descriptions-item label="浏览量">{{ detailDialog.data.views }}</el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getExamineStatusType(detailDialog.data.examineStatus)" size="small">
              {{ getExamineStatusLabel(detailDialog.data.examineStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="上架状态">
            <el-tag :type="getStuffStatusType(detailDialog.data.stuffStatus)" size="small">
              {{ getStuffStatusLabel(detailDialog.data.stuffStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ detailDialog.data.createTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="2">{{ detailDialog.data.updateTime || '无' }}</el-descriptions-item>
          <el-descriptions-item label="地址信息" :span="2">
            <div v-if="detailDialog.data.address">
              {{ detailDialog.data.address }}
            </div>
            <div v-else>无地址信息</div>
          </el-descriptions-item>
          <el-descriptions-item label="位置坐标" :span="2">
            <div v-if="detailDialog.data.lat && detailDialog.data.lng">
              纬度: {{ detailDialog.data.lat }}, 经度: {{ detailDialog.data.lng }}
            </div>
            <div v-else>无位置坐标</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 商品图片 -->
        <div v-if="detailDialog.data.media" class="image-section">
          <h4>商品图片</h4>
          <div class="image-gallery">
            <el-image
              v-for="(imageUrl, index) in getImageUrlList(detailDialog.data.media)"
              :key="index"
              :src="imageUrl"
              style="width: 120px; height: 90px; margin-right: 10px; margin-bottom: 10px;"
              fit="cover"
              :preview-src-list="getImageUrlList(detailDialog.data.media)"
              :initial-index="index"    :preview-teleported="true"
            />
          </div>
        </div>

        <!-- 状态管理操作 -->
        <div class="audit-section">
          <h4>审核状态管理</h4>
          <div class="audit-buttons">
            <template v-for="status in examineStatusOptions" :key="status.value">
              <el-button
                v-if="detailDialog.data.examineStatus !== status.value"
                type="primary"
                size="large"
                @click="auditGoods(detailDialog.data, status.value)"
              >
                <el-icon><clock /></el-icon>
                {{ status.label }}
              </el-button>
            </template>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listGoodStuff, getGoodStuff, deleteGoodStuff, auditGoodStuff } from '@/api/goods/manageGoodStuff'
import { listDictByNameEn } from '@/api/system/dict'
import { ArrowDown, Check, Close, Clock } from '@element-plus/icons-vue'

export default {
  name: 'GoodsList',

  components: {
    ArrowDown,
    Check,
    Close,
    Clock
  },

  data() {
    return {
      // 搜索条件
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        stuffDescribe: '',
        examineStatus: '',
        type: ''
      },
      // 好物列表
      goodsList: [],
      // 总数
      total: 0,
      // 加载状态
      loading: false,
      // 图片服务器地址
      imgServer: import.meta.env.VITE_BASE_API + "/common-api/v1/file/",
      // 详情弹窗
      detailDialog: {
        visible: false,
        title: '好物详情',
        data: null
      },
      // 数据字典选项
      examineStatusOptions: [], // 审核状态字典
      stuffStatusOptions: [],   // 上架状态字典
      typeOptions: [],
      categoryOptions: []
    }
  },

  methods: {
    /**
     * 初始化数据字典
     */
    async initDictData() {
      try {
        // 加载好物审核状态字典
        const examineStatusRes = await listDictByNameEn('good_examin_status')
        this.examineStatusOptions = (examineStatusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载好物上架状态字典
        const stuffStatusRes = await listDictByNameEn('good_stuff_status')
        this.stuffStatusOptions = (stuffStatusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载好物类型字典
        const typeRes = await listDictByNameEn('good_stuff_type')
        this.typeOptions = (typeRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 加载好物分类字典
        const categoryRes = await listDictByNameEn('good_stuff_category')
        this.categoryOptions = (categoryRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
      } catch (err) {
        console.error('加载数据字典失败:', err)
      }
    },

    /**
     * 搜索好物列表
     */
    search() {
      this.loading = true

      // 构建查询参数，过滤空值
      const params = {}
      Object.keys(this.searchModel).forEach(key => {
        if (this.searchModel[key] !== '' && this.searchModel[key] !== null && this.searchModel[key] !== undefined) {
          params[key] = this.searchModel[key]
        }
      })

      listGoodStuff(params).then(res => {
        this.goodsList = res.data.data.list || []
        this.total = res.data.data.total || 0
      }).catch(err => {
        console.error('获取好物列表失败:', err)
        this.$message.error(err.data?.errorMessage || '获取好物列表失败')
      }).finally(() => {
        this.loading = false
      })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        stuffDescribe: '',
        examineStatus: '',
        type: ''
      }
      this.search()
    },

    /**
     * 查看详情
     */
    viewDetail(row) {
      getGoodStuff(row.id).then(res => {
        this.detailDialog.data = res.data.data
        this.detailDialog.visible = true
      }).catch(err => {
        console.error('获取好物详情失败:', err)
        this.$message.error(err.data?.errorMessage || '获取好物详情失败')
      })
    },

    /**
     * 删除好物
     */
    deleted(id) {
      this.$confirm('确定要删除这个好物吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteGoodStuff(id).then(() => {
          this.search()
          this.$message.success('删除成功')
        }).catch(err => {
          console.error('删除好物失败:', err)
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {
        // 用户取消删除
      })
    },

    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 审核状态管理操作
     */
    auditGoods(row, newExamineStatus) {
      // 获取当前状态和新状态的中文名称
      const currentStatusOption = this.examineStatusOptions.find(item => item.value === row.examineStatus)
      const newStatusOption = this.examineStatusOptions.find(item => item.value === newExamineStatus)

      const currentStatusText = currentStatusOption ? currentStatusOption.label : row.examineStatus
      const newStatusText = newStatusOption ? newStatusOption.label : newExamineStatus
      const actionText = `从"${currentStatusText}"变更为"${newStatusText}"`

      this.$prompt(`确定要将这个好物${actionText}吗？请输入审核备注：`, '审核状态变更确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入审核备注（可选）',
        inputValidator: null, // 备注可选，不强制验证
        dangerouslyUseHTMLString: true,
        message: `
          <div>
            <p><strong>商品：</strong>${row.stuffDescribe}</p>
            <p><strong>当前状态：</strong><span style="color: #E6A23C;">${currentStatusText}</span></p>
            <p><strong>变更为：</strong><span style="color: #67C23A;">${newStatusText}</span></p>
            <p style="color: #E6A23C; margin-top: 10px;">此操作将改变商品审核状态，请谨慎操作！</p>
          </div>
        `
      }).then(({ value: examineNote }) => {
        // 构建审核数据
        const auditData = {
          id: row.id,
          examineStatus: newExamineStatus,
          examineNote: examineNote || ''
        }

        console.log('审核数据:', auditData)

        auditGoodStuff(auditData).then(res => {
          console.log('审核成功响应:', res)
          this.search()
          this.detailDialog.visible = false
          this.$message.success(`商品审核状态已${actionText}`)
        }).catch(err => {
          console.error(`审核失败:`, err)
          const errorMsg = err.response?.data?.errorMessage || err.data?.errorMessage || `审核失败`
          this.$message.error(errorMsg)
        })
      }).catch(() => {
        // 用户取消操作
      })
    },

    /**
     * 获取审核状态标签
     */
    getExamineStatusLabel(examineStatus) {
      const option = this.examineStatusOptions.find(item => item.value === examineStatus)
      return option ? option.label : examineStatus
    },

    /**
     * 获取审核状态类型
     */
    getExamineStatusType(examineStatus) {
      // 根据字典数据的cssClass或使用默认映射
      const option = this.examineStatusOptions.find(item => item.value === examineStatus)
      if (option && option.cssClass) {
        // 如果字典有cssClass，可以根据颜色映射到Element Plus的类型
        const colorTypeMap = {
          '#E6A23C': 'warning',
          '#67C23A': 'success',
          '#F56C6C': 'danger',
          '#409EFF': 'primary'
        }
        return colorTypeMap[option.cssClass] || 'info'
      }
      // 默认映射
      const statusMap = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger'
      }
      return statusMap[examineStatus] || 'info'
    },

    /**
     * 获取上架状态标签
     */
    getStuffStatusLabel(stuffStatus) {
      const option = this.stuffStatusOptions.find(item => item.value === stuffStatus)
      return option ? option.label : stuffStatus
    },

    /**
     * 获取上架状态类型
     */
    getStuffStatusType(stuffStatus) {
      // 根据字典数据的cssClass或使用默认映射
      const option = this.stuffStatusOptions.find(item => item.value === stuffStatus)
      if (option && option.cssClass) {
        const colorTypeMap = {
          '#E6A23C': 'warning',
          '#67C23A': 'success',
          '#F56C6C': 'danger',
          '#409EFF': 'primary'
        }
        return colorTypeMap[option.cssClass] || 'info'
      }
      // 默认映射
      const statusMap = {
        'listed': 'success',
        'unlisted': 'danger',
        'pending': 'warning'
      }
      return statusMap[stuffStatus] || 'info'
    },

    /**
     * 获取类型标签
     */
    getTypeLabel(type) {
      const option = this.typeOptions.find(item => item.value === type)
      return option ? option.label : type
    },

    /**
     * 获取分类标签
     */
    getCategoryLabel(categoryCode) {
      const option = this.categoryOptions.find(item => item.value === categoryCode)
      return option ? option.label : categoryCode
    },

    /**
     * 获取第一张图片URL（用于列表显示）
     */
    getFirstImageUrl(mediaString) {
      if (!mediaString) return ''
      const imageUrls = this.getImageUrlList(mediaString)
      return imageUrls.length > 0 ? imageUrls[0] : ''
    },

    /**
     * 获取图片URL列表（用于预览）
     */
    getImageUrlList(mediaString) {
      if (!mediaString) return []

      // 分割逗号分隔的图片路径
      const imagePaths = mediaString.split(',').filter(path => path.trim())

      return imagePaths.map(path => {
        const trimmedPath = path.trim()
        // 如果已经是完整URL，直接返回
        if (trimmedPath.startsWith('http')) {
          return trimmedPath
        }
        // 否则拼接服务器地址
        return this.imgServer + trimmedPath
      })
    },

    /**
     * 获取图片URL（兼容方法）
     */
    getImageUrl(imageUrl) {
      if (!imageUrl) return ''
      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }
      // 否则拼接服务器地址
      return this.imgServer + imageUrl
    }
  },

  async created() {
    await this.initDictData()
    this.search()
  }
}
</script>

<style scoped>
.goods-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.goods-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

.card--search {
  flex-shrink: 0;
}

.card--table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.price-text {
  color: #e74c3c;
  font-weight: bold;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

.description-text {
  max-height: 100px;
  overflow-y: auto;
  line-height: 1.5;
  word-break: break-all;
}

.image-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.image-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-gallery .el-image {
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.image-gallery .el-image:hover {
  transform: scale(1.05);
}

.audit-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.audit-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
}

.audit-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.audit-buttons .el-button {
  min-width: 140px;
  padding: 12px 20px;
}

.audit-buttons .el-button .el-icon {
  margin-right: 5px;
}

/* 暗色主题适配 */
.dark-theme .card {
  background: var(--card-background);
  color: var(--text-primary);
}

.dark-theme .price-text {
  color: #ff6b6b;
}

.dark-theme .image-section {
  border-top-color: #444;
}

.dark-theme .image-section h4 {
  color: var(--text-primary);
}

.dark-theme .audit-section {
  border-top-color: #444;
}

.dark-theme .audit-section h4 {
  color: var(--text-primary);
}

.dark-theme .audit-buttons .el-button {
  border-color: var(--border-color);
}

/* 图片预览层级设置 - 确保在最顶层 */
:deep(.el-image-viewer__wrapper) {
  z-index: 9999 !important;
}

:deep(.el-image-viewer__mask) {
  z-index: 9998 !important;
}

:deep(.el-image-viewer__btn) {
  z-index: 10000 !important;
}

:deep(.el-image-viewer__canvas) {
  z-index: 9999 !important;
}

/* 确保图片预览工具栏在最顶层 */
:deep(.el-image-viewer__actions) {
  z-index: 10001 !important;
}

/* 确保图片预览关闭按钮在最顶层 */
:deep(.el-image-viewer__close) {
  z-index: 10002 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-flex {
    flex-direction: column;
    align-items: stretch;
  }

  .search-flex > * {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .search-flex > *:last-child {
    margin-bottom: 0;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .audit-buttons {
    flex-direction: column;
    align-items: center;
  }

  .audit-buttons .el-button {
    width: 100%;
    max-width: 280px;
  }
}
</style>