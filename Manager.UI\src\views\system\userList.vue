<template>
	<div class="user-list-container">
		<user-edit ref="userEditNew" :statusList="statusList" @search="search"></user-edit>
		<div class="card card--search search-flex">
			<el-input v-model="searchModel.keyword" placeholder="用户名|昵称" clearable style="width: 200px; margin-right: 16px;" />
			<el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
			<el-button type="primary" @click="add">添加</el-button>
			
		</div>
		<div class="card card--table">
			<div class="table-col">
				<el-table stripe :data="userList" style="width: 100%; height: 100%;" class="data-table">
					<el-table-column prop="userName" align="center" label="用户名" />
					<el-table-column prop="roleId" align="center" label="角色" :formatter="formatRole" />
					<el-table-column prop="phone" align="center" label="手机号" />
			
					<el-table-column prop="status" align="center" :formatter="formatStatus" label="状态" />
					<el-table-column prop="createTime" align="center" label="创建时间" />
					<el-table-column prop="updateTime" align="center" label="更新时间" />
					<el-table-column align="center" width="160" label="操作">
						<template #default="scope">
							<el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
							<el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div class="pagination-col">
				<el-pagination background layout="prev, pager, next" @current-change="currentChange" @prev-click="prevClick"
					@next-click="nextClick" :total="total"></el-pagination>
			</div>
		</div>
	</div>
</template>
<script>
import { listUser, deleteUser, getUser } from "@/api/system/user"
import { listDictByNameEn } from "@/api/system/dict"
import mitt from "@/utils/mitt"
import userEdit from "@/components/system/userEdit.vue"
import { listRole } from '@/api/system/role'
export default {
	components: { userEdit  },
	data() {
		return {
			searchModel: {
				pageNum: 1,
				pageSize: 10
			},
			roleList: [],
			userList: [],
			statusList: [],
			
			total: 0
		}
	},
	methods: {
		search() {
			listUser(this.searchModel)
				.then(res => {
					this.userList = res.data.data.list
					this.total = res.data.data.total
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		add() {
			console.log('点击添加用户按钮');
			mitt.emit('openUserAdd');
			console.log('已发送 openUserAdd 事件');
		},
		edit(id) {
			getUser(id)
				.then(res => {
					mitt.emit('openUserEdit', res.data.data)
				})
				.catch(err => {
					this.$message.error(err.data.errorMessage)
				})
		},
		deleted(id) {
			this.$confirm('删除用户, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				deleteUser(id)
					.then(res => {
						this.search()
						this.$message.success("操作成功")
					})
					.catch(err => {
						this.$message.error(err.data.errorMessage)
					})
			}).catch(() => { })
		},
		currentChange(num) {
			this.searchModel.pageNum = num
			this.search()
		},
		prevClick(newPage) {
			this.searchModel.pageNum = newPage
			this.search()
		},
		nextClick(newPage) {
			this.searchModel.pageNum = newPage
			this.search()
		},
		formatStatus(row, column, cellValue, index) {
			let result = ''
			for (let item of this.statusList) {
				if (item.nameEn == cellValue) {
					result = item.nameCn
					break
				}
			}
			return result
		},
		formatRole(row, column, cellValue, index) {
			let result = ''
			for (let item of this.roleList) {
				if (item.id == cellValue) {
					result = item.roleName
					break
				}
			}
			return result
		},
		async init() {
			try {
				const [ status_res,role_res, user_res] = await Promise.all([
				
					listDictByNameEn('sys_user_status'),
					listRole({ pageNum: 1, pageSize: 500 }),
					listUser(this.searchModel)
				])
				debugger
				this.statusList = status_res.data.data
				this.roleList = role_res.data.data.list

				this.userList = user_res.data.data.list
				this.total = user_res.data.data.total
			}catch (err) {
				debugger
				this.$message.error(err.data.errorMessage)
			}
		}
	},
	created() {
		this.init()
	},
	unmounted() {
		mitt.off('openUserAdd')
		mitt.off('openUserEdit')
	}
}
</script>

<style scoped>
.user-list-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;
}

.card--table {
	background-color: #fff;
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	flex: 1;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: auto;
	margin-top: 0;
}

.table-col {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.data-table {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100% !important;
}

.pagination-col {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
}



.search-flex {
	display: flex;
	align-items: center;
}

.card--search {
	margin-bottom: 20px;
	flex: none;
	height: auto;
	padding: 20px 20px;
	display: flex;
	align-items: center;
}
</style>