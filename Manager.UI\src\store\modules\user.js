import router from '@/router/index'

import {
	currentUser
} from "@/api/system/user"
import {
	loadMenu
} from '@/api/system/menu'


const modules = import.meta.glob("../../views/**/*.vue")

const data = {
	userinfo: {},
	permissions: [],
	treePermissions: [], // 树形权限结构
	logined: false,
	// 小区相关状态
	selectedCommunity: null, // 当前选中的小区
	communityList: [] // 小区列表
}

const reload = async () => {
	return await onLoad(true)
}


const getChildren = (route, routerList) => {
	let list = []
	if (routerList == null || routerList.length == 0) {
		return list
	}
	for (let item of routerList) {
		// 比较 ID 时考虑字符串和数字类型
		if (String(route.id) === String(item.parentId)) {
			list.push({
				id: item.id,
				path: item.path,
				menuName: item.menuName,
				sort: item.sort,
				children: getChildren(item, routerList),
				icon: item.icon,
				type: item.type,
				menuType: item.menuType,
				parentId: item.parentId,
				componentPath: item.componentPath
			})
		}
	}
	// 按 sort 字段排序
	return list.sort((a, b) => a.sort - b.sort)
}


async function onLoad(toPath) {
	console.log('onLoad 开始执行，toPath:', toPath)

	// 从 Pinia 获取 token（不依赖 userName）
	let token = window.$local?.get('smartPropertyToken')

	// 如果 token 是字符串，尝试解析并检查过期
	if (token && typeof token === 'string') {
		try {
			const tokenObj = JSON.parse(token)
			// 检查 token 是否过期
			if (tokenObj.expires_time && tokenObj.expires_time < Date.now()) {
				console.log('Token 已过期')
				token = null
			}
		} catch (e) {
			console.log('Token 解析失败，可能是旧格式')
			token = null
		}
	}

	console.log('从 Pinia 获取token:', { hasToken: !!token, tokenType: typeof token })

	if (token) {
		try{
			console.log('开始获取用户信息和权限...')
			const [user_res, permission_res] = await Promise.all([
				currentUser(),
				loadMenu()
			])
			console.log('用户信息和权限获取成功:', { user: user_res.data.data, permissions: permission_res.data.data })
			data.logined = true
			data.userinfo = user_res.data.data
			data.permissions = permission_res.data.data

			// 将用户信息和权限存储到 Pinia
			window.$local?.setSmartPropertyUserInfo(user_res.data.data)
			window.$local?.setFrontPermissions(permission_res.data.data)

			// 筛选菜单类型的权限（menuType: "menu"）
			const menuPermissions = data.permissions.filter(item => {
				console.log('检查菜单项:', item.menuName, 'menuType:', item.menuType)
				return item.menuType === 'menu'
			})
			console.log('筛选出的菜单权限:', menuPermissions)
			console.log('原始权限数据:', data.permissions)

			// 动态添加路由（只添加有组件路径的菜单）
			for (let item of menuPermissions) {
				if (item.componentPath) {
					const component = modules['../../views/' + item.componentPath + '.vue']
					if (component) {
						router.addRoute('index', {
							path: '/' + item.path,
							component: component
						})
					}
				}
			}

			// 构建菜单树（按 sort 排序）
			let list = []
			for (let item of menuPermissions) {
				// 查找顶级菜单（parentId 为 "0" 或 0）
				if (item.parentId == "0" || item.parentId == 0 || item.parentId === null) {
					list.push({
						id: item.id,
						path: item.path,
						menuName: item.menuName,
						sort: item.sort,
						children: getChildren(item, menuPermissions),
						icon: item.icon,
						type: item.type,
						menuType: item.menuType,
						componentPath: item.componentPath
					})
				}
			}
			// 按 sort 字段排序
			data.treePermissions = list.sort((a, b) => a.sort - b.sort)

			// 将构建好的菜单树也存储到 Pinia
			window.$local?.setTreePermissions(data.treePermissions)

			console.log('构建的菜单树:', data.treePermissions)
			
			// 移除自动跳转逻辑，让调用方控制跳转
			// if(toPath){
			// 	router.push("/index")
			// }
		}catch(err){
			console.error('获取用户信息失败:', err)
			data.logined = false
		}
	} else {
		console.log('没有token，设置为未登录状态')
		data.logined = false
	}
	console.log('onLoad 执行完成，logined:', data.logined)
}


await onLoad(false)


router.beforeEach((to, from, next) => {
	// 从 Pinia 检查token是否存在（不依赖 userName）
	let token = window.$local?.get('smartPropertyToken')

	// 检查 token 格式和有效性
	if (token && typeof token === 'string') {
		try {
			const tokenObj = JSON.parse(token)
			// 检查 token 是否过期
			if (tokenObj.expires_time && tokenObj.expires_time < Date.now()) {
				console.log('路由守卫: Token 已过期')
				token = null
			}
		} catch (e) {
			console.log('路由守卫: Token 解析失败')
			token = null
		}
	}

	const hasToken = token && token !== 'null' && token !== 'undefined'

	console.log('路由守卫检查:', {
		toPath: to.path,
		fromPath: from.path,
		hasToken,
		logined: data.logined
	})

	// 如果已登录且访问登录页，重定向到首页
	if(to.path === "/login" && data.logined && hasToken){
		console.log('已登录用户访问登录页，重定向到首页')
		return next("/home")
	}
	// 如果未登录且访问非登录页，重定向到登录页
	else if(to.path !== "/login" && (!data.logined || !hasToken)) {
		console.log('未登录用户访问受保护页面，重定向到登录页')
		window.$local?.removeAll()
		data.logined = false
		return next("/login")
	}

	console.log('路由守卫通过，继续导航')
	return next()
})

// 小区相关方法
const setCommunityList = (list) => {
	data.communityList = list
	// 如果有小区数据且没有选中的小区，默认选择第一个
	if (list && list.length > 0 && !data.selectedCommunity) {
		data.selectedCommunity = list[0]
	}
}

const setSelectedCommunity = (community) => {
	data.selectedCommunity = community
}

const clearCommunityData = () => {
	data.selectedCommunity = null
	data.communityList = []
}

export default data

export {
	reload,
	setCommunityList,
	setSelectedCommunity,
	clearCommunityData
}