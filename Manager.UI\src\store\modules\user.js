import router from '@/router/index'

import {
	currentUser
} from "@/api/system/user"
import {
	loadMenu
} from '@/api/system/menu'


const modules = import.meta.glob("../../views/**/*.vue")
// 只在需要时显示可用模块
if (window.location.search.includes('debug=true')) {
	console.log('可用的组件模块:', Object.keys(modules))
}

const data = {
	userinfo: {},
	permissions: [],
	treePermissions: [], // 树形权限结构
	logined: false,
	// 小区相关状态
	selectedCommunity: null, // 当前选中的小区
	communityList: [] // 小区列表
}

const reload = async () => {
	return await onLoad(true)
}


const getChildren = (route, routerList) => {
	let list = []
	if (routerList == null || routerList.length == 0) {
		return list
	}
	for (let item of routerList) {
		// 比较 ID 时考虑字符串和数字类型
		if (String(route.id) === String(item.parentId)) {
			list.push({
				id: item.id,
				path: item.path,
				menuName: item.menuName,
				sort: item.sort,
				children: getChildren(item, routerList),
				icon: item.icon,
				type: item.type,
				menuType: item.menuType,
				parentId: item.parentId,
				componentPath: item.componentPath
			})
		}
	}
	// 按 sort 字段排序
	return list.sort((a, b) => a.sort - b.sort)
}


async function onLoad(toPath) {
	console.log('onLoad 开始执行，toPath:', toPath)

	// 从 Pinia 获取 token（不依赖 userName）
	let token = window.$local?.get('smartPropertyToken')

	// 如果 token 是字符串，尝试解析并检查过期
	if (token && typeof token === 'string') {
		try {
			const tokenObj = JSON.parse(token)
			// 检查 token 是否过期
			if (tokenObj.expires_time && tokenObj.expires_time < Date.now()) {
				console.log('Token 已过期')
				token = null
			}
		} catch (e) {
			console.log('Token 解析失败，可能是旧格式')
			token = null
		}
	}

	console.log('从 Pinia 获取token:', { hasToken: !!token, tokenType: typeof token })

	if (token) {
		try{
			console.log('开始获取用户信息和权限...')
			const [user_res, permission_res] = await Promise.all([
				currentUser(),
				loadMenu()
			])
			console.log('用户信息和权限获取成功:', { user: user_res.data.data, permissions: permission_res.data.data })
			data.logined = true
			data.userinfo = user_res.data.data
			data.permissions = permission_res.data.data

			// 将用户信息和权限存储到 Pinia
			window.$local?.setSmartPropertyUserInfo(user_res.data.data)
			window.$local?.setFrontPermissions(permission_res.data.data)

			// 筛选菜单类型的权限（menuType: "menu"）
			const menuPermissions = data.permissions.filter(item => item.menuType === 'menu')
			console.log('筛选出的菜单权限数量:', menuPermissions.length)

			// 动态添加路由（只添加有组件路径的菜单）
			const isDebug = window.location.search.includes('debug=true')
			if (isDebug) {
				console.log('🔧 调试模式：所有菜单权限:', menuPermissions)
			}

			for (let item of menuPermissions) {
				if (item.componentPath) {
					const componentPath = '../../views/' + item.componentPath + '.vue'
					const component = modules[componentPath]

					if (isDebug) {
						console.log('🔧 调试路由:', {
							menuName: item.menuName,
							path: '/' + item.path,
							componentPath: componentPath,
							componentFound: !!component,
							availableComponents: Object.keys(modules).filter(key => key.includes(item.componentPath))
						})
					}

					if (component) {
						router.addRoute('index', {
							path: '/' + item.path,
							component: component
						})
						console.log('✅ 添加路由:', '/' + item.path)
					} else {
						console.error('❌ 组件未找到:', componentPath, '菜单:', item.menuName)
						if (isDebug) {
							console.log('🔧 可能的组件路径:', Object.keys(modules).filter(key =>
								key.toLowerCase().includes(item.menuName.toLowerCase()) ||
								key.includes(item.path)
							))
						}
					}
				}
			}

			// 构建菜单树（按 sort 排序）
			let list = []
			for (let item of menuPermissions) {
				// 查找顶级菜单（parentId 为 "0" 或 0）
				if (item.parentId == "0" || item.parentId == 0 || item.parentId === null) {
					list.push({
						id: item.id,
						path: item.path,
						menuName: item.menuName,
						sort: item.sort,
						children: getChildren(item, menuPermissions),
						icon: item.icon,
						type: item.type,
						menuType: item.menuType,
						componentPath: item.componentPath
					})
				}
			}
			// 按 sort 字段排序
			data.treePermissions = list.sort((a, b) => a.sort - b.sort)

			// 将构建好的菜单树也存储到 Pinia
			window.$local?.setTreePermissions(data.treePermissions)

			console.log('构建的菜单树:', data.treePermissions)
			
			// 移除自动跳转逻辑，让调用方控制跳转
			// if(toPath){
			// 	router.push("/index")
			// }
		}catch(err){
			console.error('获取用户信息失败:', err)
			data.logined = false
		}
	} else {
		console.log('没有token，设置为未登录状态')
		data.logined = false
	}
	console.log('onLoad 执行完成，logined:', data.logined)
}


// 注册路由守卫（在模块加载时立即注册，不等待 onLoad）
router.beforeEach(async (to, from, next) => {
	console.log('🔍 路由守卫开始执行:', to.path)

	// 如果访问登录页，直接允许
	if (to.path === "/login") {
		console.log('访问登录页，直接允许')
		return next()
	}

	// 确保 Pinia 数据已经恢复
	if (!window.$local) {
		console.log('Pinia 还未初始化，等待...')
		await new Promise(resolve => setTimeout(resolve, 100))
	}

	// 从 Pinia 检查token是否存在
	let token = window.$local?.get('smartPropertyToken')
	console.log('获取到的 token:', token ? '存在' : '不存在', typeof token)

	// 检查 token 格式和有效性
	if (token && typeof token === 'string') {
		try {
			const tokenObj = JSON.parse(token)
			if (tokenObj.expires_time && tokenObj.expires_time < Date.now()) {
				console.log('路由守卫: Token 已过期')
				token = null
			}
		} catch (e) {
			console.log('路由守卫: Token 解析失败')
			token = null
		}
	}

	const hasToken = token && token !== 'null' && token !== 'undefined'

	// 如果没有 token，重定向到登录页
	if (!hasToken) {
		console.log('没有有效 token，重定向到登录页')
		window.$local?.removeAll()
		data.logined = false
		return next("/login")
	}

	// 如果有 token 但还没有加载用户信息，先加载
	if (hasToken && !data.logined) {
		console.log('有 token 但未登录，尝试加载用户信息')
		try {
			await onLoad(to.path)
			console.log('用户信息加载成功，继续导航')
			// 加载完成后，路由已经添加，直接继续
			return next()
		} catch (error) {
			console.log('用户信息加载失败，重定向到登录页')
			window.$local?.removeAll()
			data.logined = false
			return next("/login")
		}
	}

	// 如果已登录，检查目标路由是否存在
	const targetRoute = router.getRoutes().find(route => route.path === to.path)
	if (!targetRoute) {
		console.error('❌ 目标路由不存在:', to.path)
		console.log('📋 当前所有路由:', router.getRoutes().map(r => r.path))

		// 尝试从 Pinia 恢复路由
		const permissions = window.$local?.get('frontPermissions')
		if (permissions && permissions.length > 0) {
			console.log('🔄 尝试恢复缺失的路由...')
			const menuPermissions = permissions.filter(item => item.menuType === 'menu')
			let routeAdded = false

			for (let item of menuPermissions) {
				if (item.componentPath && '/' + item.path === to.path) {
					const componentPath = '../../views/' + item.componentPath + '.vue'
					const component = modules[componentPath]
					if (component) {
						router.addRoute('index', {
							path: '/' + item.path,
							component: component
						})
						console.log('🔄 成功恢复路由:', '/' + item.path)
						routeAdded = true
						break
					} else {
						console.error('❌ 组件未找到:', componentPath)
					}
				}
			}

			if (routeAdded) {
				// 路由已添加，重新导航
				return next(to.path)
			}
		}

		// 如果还是找不到，回退到首页
		return next('/home')
	}

	console.log('🚀 路由守卫通过，继续导航到:', to.path)
	return next()
})

// 小区相关方法
const setCommunityList = (list) => {
	data.communityList = list
	// 如果有小区数据且没有选中的小区，默认选择第一个
	if (list && list.length > 0 && !data.selectedCommunity) {
		data.selectedCommunity = list[0]
	}
}

const setSelectedCommunity = (community) => {
	data.selectedCommunity = community
}

const clearCommunityData = () => {
	data.selectedCommunity = null
	data.communityList = []
}

// 初始化时尝试恢复用户状态（不重复添加路由）
setTimeout(async () => {
	const token = window.$local?.get('smartPropertyToken')
	const permissions = window.$local?.get('frontPermissions')
	const userInfo = window.$local?.get('smartPropertyUserInfo')

	console.log('🚀 应用启动检查:', { hasToken: !!token, hasPermissions: !!permissions, hasUserInfo: !!userInfo })

	// 如果有完整的用户数据，恢复登录状态
	if (token && permissions && userInfo) {
		data.logined = true
		data.userinfo = userInfo
		data.permissions = permissions

		// 重新构建菜单树
		const menuPermissions = permissions.filter(item => item.menuType === 'menu')
		let list = []
		for (let item of menuPermissions) {
			if (item.parentId == "0" || item.parentId == 0 || item.parentId === null) {
				list.push({
					id: item.id,
					path: item.path,
					menuName: item.menuName,
					sort: item.sort,
					children: getChildren(item, menuPermissions),
					icon: item.icon,
					type: item.type,
					menuType: item.menuType,
					componentPath: item.componentPath
				})
			}
		}
		data.treePermissions = list.sort((a, b) => a.sort - b.sort)
		console.log('✅ 应用启动时恢复登录状态和菜单树')
	}
}, 50)

export default data

export {
	reload,
	setCommunityList,
	setSelectedCommunity,
	clearCommunityData
}