import router from '@/router/index'

import {
	currentUser
} from "@/api/system/user"
import {
	loadMenu
} from '@/api/system/menu'


const modules = import.meta.glob("../../views/**/*.vue")

const data = {
	userinfo: {},
	permissions: [],
	logined: false,
	// 小区相关状态
	selectedCommunity: null, // 当前选中的小区
	communityList: [] // 小区列表
}

const reload = async () => {
	return await onLoad(true)
}


const getChildren = (route, routerList) => {
	let list = []
	if (routerList == null || routerList.length == 0) {
		return list
	}
	for (let item of routerList) {
		if (route.id == item.parentId) {
			list.push({
				path: item.path,
				menuName: item.menuName,
				sort: item.sort,
				children: getChildren(item, routerList),
				icon: item.icon,
				type: item.type
			})
		}
	}
	return list.sort((a, b) => a.sort - b.sort)
}


async function onLoad(toPath) {
	var token = localStorage.getItem("smart_property_token"+localStorage.getItem("userName"))
	if (token) {
		try{
			const [user_res, permission_res] = await Promise.all([
				currentUser(),
				loadMenu()
			])
			data.logined = true
			data.userinfo = user_res.data.data
			data.permissions = permission_res.data.data

			for (let item of data.permissions) {
				const component = modules['../../views/' + item.componentPath + '.vue']
				if (component) {
					router.addRoute('index', {
						path: '/' + item.path,
						component: component
					})
				}
			}
			
			let list = []
			for (let item of data.permissions) {
				if (item.parentId == 0) {
					list.push({
						path: item.path,
						menuName: item.menuName,
						sort: item.sort,
						children: getChildren(item, data.permissions),
						icon: item.icon,
						type: item.type
					})
				}
			}
			data.treePermissions = list.sort((a, b) => a.sort - b.sort)
			
			// 移除自动跳转逻辑，让调用方控制跳转
			// if(toPath){
			// 	router.push("/index")
			// }
		}catch(err){
			data.logined = false
		}
	} else {
		data.logined = false
	}
}


await onLoad(false)


router.beforeEach((to, from, next) => {
	// 检查token是否存在
	const token = localStorage.getItem("smart_property_token"+localStorage.getItem("userName"))
	const hasToken = token && token !== 'null' && token !== 'undefined'

	// 如果已登录且访问登录页，重定向到首页
	if(to.path === "/login" && data.logined && hasToken){
		return next("/home")
	}
	// 如果未登录且访问非登录页，重定向到登录页
	else if(to.path !== "/login" && (!data.logined || !hasToken)) {
		localStorage.clear()
		data.logined = false
		return next("/login")
	}
	return next()
})

// 小区相关方法
const setCommunityList = (list) => {
	data.communityList = list
	// 如果有小区数据且没有选中的小区，默认选择第一个
	if (list && list.length > 0 && !data.selectedCommunity) {
		data.selectedCommunity = list[0]
	}
}

const setSelectedCommunity = (community) => {
	data.selectedCommunity = community
}

const clearCommunityData = () => {
	data.selectedCommunity = null
	data.communityList = []
}

export default data

export {
	reload,
	setCommunityList,
	setSelectedCommunity,
	clearCommunityData
}