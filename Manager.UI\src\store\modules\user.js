import router from '@/router/index'

import {
	currentUser
} from "@/api/system/user"
import {
	loadMenu
} from '@/api/system/menu'


const modules = import.meta.glob("../../views/**/*.vue")

const data = {
	userinfo: {},
	permissions: [],
	treePermissions: [], // 树形权限结构
	logined: false,
	// 小区相关状态
	selectedCommunity: null, // 当前选中的小区
	communityList: [] // 小区列表
}

const reload = async () => {
	return await onLoad(true)
}


const getChildren = (route, routerList) => {
	let list = []
	if (routerList == null || routerList.length == 0) {
		return list
	}
	for (let item of routerList) {
		if (route.id == item.parentId) {
			list.push({
				id: item.id,
				path: item.path,
				menuName: item.menuName,
				sort: item.sort,
				children: getChildren(item, routerList),
				icon: item.icon,
				type: item.type,
				menuType: item.menuType,
				parentId: item.parentId
			})
		}
	}
	return list.sort((a, b) => a.sort - b.sort)
}


async function onLoad(toPath) {
	console.log('onLoad 开始执行，toPath:', toPath)

	// 从 Pinia 获取用户信息和 token
	const userName = window.$local?.get('userName')
	let token = userName ? window.$local?.get('smartPropertyToken') : null

	// 如果 token 是字符串，尝试解析
	if (token && typeof token === 'string') {
		try {
			const tokenObj = JSON.parse(token)
			// 检查 token 是否过期
			if (tokenObj.expires_time && tokenObj.expires_time < Date.now()) {
				console.log('Token 已过期')
				token = null
			}
		} catch (e) {
			console.log('Token 解析失败，可能是旧格式')
		}
	}

	console.log('从 Pinia 获取token:', { userName, hasToken: !!token, tokenType: typeof token })

	if (token) {
		try{
			console.log('开始获取用户信息和权限...')
			const [user_res, permission_res] = await Promise.all([
				currentUser(),
				loadMenu()
			])
			console.log('用户信息和权限获取成功:', { user: user_res.data.data, permissions: permission_res.data.data })
			data.logined = true
			data.userinfo = user_res.data.data
			data.permissions = permission_res.data.data

			// 将用户信息和权限存储到 Pinia
			window.$local?.setSmartPropertyUserInfo(user_res.data.data)
			window.$local?.setFrontPermissions(permission_res.data.data)

			// 过滤有效的菜单权限（状态为启用且类型为菜单或按钮）
			const validPermissions = data.permissions.filter(item => {
				// 检查状态：只显示启用的菜单
				if (item.status && item.status !== 'enable' && item.status !== '1' && item.status !== 1) {
					return false
				}

				// 检查类型：只处理菜单类型的项目
				if (item.menuType && item.menuType !== 'menu' && item.menuType !== 'button') {
					return false
				}

				// 检查是否可见
				if (item.visible !== undefined && item.visible !== null && item.visible !== true && item.visible !== '1' && item.visible !== 1) {
					return false
				}

				return true
			})

			console.log('过滤后的有效权限:', validPermissions)

			// 动态添加路由（只添加有组件路径的菜单）
			for (let item of validPermissions) {
				if (item.componentPath && item.menuType === 'menu') {
					const component = modules['../../views/' + item.componentPath + '.vue']
					if (component) {
						router.addRoute('index', {
							path: '/' + item.path,
							component: component
						})
					}
				}
			}

			// 构建菜单树（只包含菜单类型的项目）
			const menuPermissions = validPermissions.filter(item => item.menuType === 'menu')
			let list = []
			for (let item of menuPermissions) {
				if (item.parentId == 0 || item.parentId === null) {
					list.push({
						id: item.id,
						path: item.path,
						menuName: item.menuName,
						sort: item.sort,
						children: getChildren(item, menuPermissions),
						icon: item.icon,
						type: item.type,
						menuType: item.menuType
					})
				}
			}
			data.treePermissions = list.sort((a, b) => a.sort - b.sort)

			console.log('构建的菜单树:', data.treePermissions)
			
			// 移除自动跳转逻辑，让调用方控制跳转
			// if(toPath){
			// 	router.push("/index")
			// }
		}catch(err){
			console.error('获取用户信息失败:', err)
			data.logined = false
		}
	} else {
		console.log('没有token，设置为未登录状态')
		data.logined = false
	}
	console.log('onLoad 执行完成，logined:', data.logined)
}


await onLoad(false)


router.beforeEach((to, from, next) => {
	// 从 Pinia 检查token是否存在
	const userName = window.$local?.get('userName')
	let token = userName ? window.$local?.get('smartPropertyToken') : null

	// 检查 token 格式和有效性
	if (token && typeof token === 'string') {
		try {
			const tokenObj = JSON.parse(token)
			// 检查 token 是否过期
			if (tokenObj.expires_time && tokenObj.expires_time < Date.now()) {
				console.log('路由守卫: Token 已过期')
				token = null
			}
		} catch (e) {
			console.log('路由守卫: Token 解析失败')
		}
	}

	const hasToken = token && token !== 'null' && token !== 'undefined'

	console.log('路由守卫检查:', {
		toPath: to.path,
		fromPath: from.path,
		hasToken,
		logined: data.logined,
		userName
	})

	// 如果已登录且访问登录页，重定向到首页
	if(to.path === "/login" && data.logined && hasToken){
		console.log('已登录用户访问登录页，重定向到首页')
		return next("/home")
	}
	// 如果未登录且访问非登录页，重定向到登录页
	else if(to.path !== "/login" && (!data.logined || !hasToken)) {
		console.log('未登录用户访问受保护页面，重定向到登录页')
		window.$local?.removeAll()
		data.logined = false
		return next("/login")
	}

	console.log('路由守卫通过，继续导航')
	return next()
})

// 小区相关方法
const setCommunityList = (list) => {
	data.communityList = list
	// 如果有小区数据且没有选中的小区，默认选择第一个
	if (list && list.length > 0 && !data.selectedCommunity) {
		data.selectedCommunity = list[0]
	}
}

const setSelectedCommunity = (community) => {
	data.selectedCommunity = community
}

const clearCommunityData = () => {
	data.selectedCommunity = null
	data.communityList = []
}

export default data

export {
	reload,
	setCommunityList,
	setSelectedCommunity,
	clearCommunityData
}