<template>
  <div class="order-list-container">
    <div class="order-list-content">
      <!-- 搜索区域 -->
      <div class="card card--search search-flex">
        <el-input
          v-model="searchModel.orderNo"
          placeholder="订单号"
          clearable
          style="width: 200px; margin-right: 16px;"
        />
        <el-input
          v-model="searchModel.stuffDescribe"
          placeholder="商品描述"
          clearable
          style="width: 150px; margin-right: 16px;"
        />
        <el-select
          v-model="searchModel.status"
          placeholder="订单状态"
          clearable
          style="width: 120px; margin-right: 16px;"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>

      <!-- 表格区域 -->
      <div class="card card--table">
        <div class="table-col">
          <el-table
            :data="orderList"
            row-key="id"
            style="width: 100%; height: 100%;"
            class="data-table"
            v-loading="loading"
          >
            <el-table-column prop="id" label="订单ID" width="80" align="center"/>
            <el-table-column prop="orderNo" label="订单号" width="180" align="center" show-overflow-tooltip/>
            <el-table-column label="商品信息" min-width="200" align="center">
              <template #default="scope">
                <div class="goods-info">
                  <div class="goods-title">{{ getStuffInfo(scope.row, 'stuffDescribe') }}</div>
                  <div class="goods-address">{{ getStuffInfo(scope.row, 'address') }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="单价" width="100" align="center">
              <template #default="scope">
                <span class="price-text">¥{{ scope.row.unitAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" align="center"/>
            <el-table-column label="总金额" width="100" align="center">
              <template #default="scope">
                <span class="price-text">¥{{ scope.row.totalAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="订单状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small" :color="getStatusColor(scope.row.status)">
                  {{ getStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="下单时间" width="160" align="center"/>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button type="text"  @click="viewDetail(scope.row)">查看</el-button>
                <el-button type="text"  @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="currentChange"
            :current-page="searchModel.pageNum"
            :page-size="searchModel.pageSize"
            layout="total, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.title"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="detailDialog.data" class="detail-content">
        <!-- 订单基本信息 -->
        <el-descriptions title="订单信息" :column="2" border>
          <el-descriptions-item label="订单ID">{{ detailDialog.data.id }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ detailDialog.data.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(detailDialog.data.status)" size="small" :color="getStatusColor(detailDialog.data.status)">
              {{ getStatusLabel(detailDialog.data.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数量">{{ detailDialog.data.quantity }}</el-descriptions-item>
          <el-descriptions-item label="单价">
            <span class="price-text">¥{{ detailDialog.data.unitAmount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="总金额">
            <span class="price-text">¥{{ detailDialog.data.totalAmount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="下单时间">{{ detailDialog.data.createTime }}</el-descriptions-item>
          <el-descriptions-item label="过期时间">{{ detailDialog.data.expireTime || '无' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            <div class="description-text">{{ detailDialog.data.note || '无' }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 商品快照信息 -->
        <el-descriptions title="商品快照" :column="2" border style="margin-top: 20px;" v-if="getStuffSnapshot(detailDialog.data)">
          <el-descriptions-item label="商品ID">{{ getStuffSnapshot(detailDialog.data).id }}</el-descriptions-item>
          <el-descriptions-item label="商品描述">{{ getStuffSnapshot(detailDialog.data).stuffDescribe }}</el-descriptions-item>
          <el-descriptions-item label="商品地址">{{ getStuffSnapshot(detailDialog.data).address }}</el-descriptions-item>
          <el-descriptions-item label="商品类型">{{ getStuffSnapshot(detailDialog.data).type }}</el-descriptions-item>
          <el-descriptions-item label="分类代码">{{ getStuffSnapshot(detailDialog.data).categoryCode }}</el-descriptions-item>
          <el-descriptions-item label="库存">{{ getStuffSnapshot(detailDialog.data).stock }}</el-descriptions-item>
          <el-descriptions-item label="已售">{{ getStuffSnapshot(detailDialog.data).sold }}</el-descriptions-item>
          <el-descriptions-item label="浏览量">{{ getStuffSnapshot(detailDialog.data).views }}</el-descriptions-item>
          <el-descriptions-item label="积分">{{ getStuffSnapshot(detailDialog.data).points }}</el-descriptions-item>
          <el-descriptions-item label="位置">
            <span v-if="getStuffSnapshot(detailDialog.data).lat && getStuffSnapshot(detailDialog.data).lng">
              {{ getStuffSnapshot(detailDialog.data).lat }}, {{ getStuffSnapshot(detailDialog.data).lng }}
            </span>
            <span v-else>无位置信息</span>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 商品图片 -->
        <div class="image-section" v-if="getStuffSnapshot(detailDialog.data) && getStuffSnapshot(detailDialog.data).media">
          <h4>商品图片</h4>
          <div class="image-gallery">
            <el-image
              v-for="(image, index) in getStuffImages(detailDialog.data)"
              :key="index"
              :src="image"
              :preview-src-list="getStuffImages(detailDialog.data)"
              :initial-index="index"
              fit="cover"
              style="width: 100px; height: 100px;"
              preview-teleported
            />
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listGoodStuffOrder, getGoodStuffOrder, deleteGoodStuffOrder } from '@/api/goods/manageGoodStuff'
import { listDictByNameEn } from '@/api/system/dict'

export default {
  name: 'GoodsOrderList',

  data() {
    return {
      // 搜索条件
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        orderNo: '',
        stuffDescribe: '',
        status: ''
      },
      // 订单列表
      orderList: [],
      // 总数
      total: 0,
      // 加载状态
      loading: false,
      // 状态选项
      statusOptions: [],
      // 详情弹窗
      detailDialog: {
        visible: false,
        title: '订单详情',
        data: null
      }
    }
  },

  methods: {
    /**
     * 搜索订单列表
     */
    search() {
      this.loading = true

      // 构建查询参数，过滤空值
      const params = {}
      Object.keys(this.searchModel).forEach(key => {
        if (this.searchModel[key] !== '' && this.searchModel[key] !== null && this.searchModel[key] !== undefined) {
          params[key] = this.searchModel[key]
        }
      })

      listGoodStuffOrder(params).then(res => {
        this.orderList = res.data.data.list || []
        this.total = res.data.data.total || 0
      }).catch(err => {
        console.error('获取订单列表失败:', err)
        this.$message.error(err.data?.errorMessage || '获取订单列表失败')
      }).finally(() => {
        this.loading = false
      })
    },

    /**
     * 重置搜索条件
     */
    resetSearch() {
      this.searchModel = {
        pageNum: 1,
        pageSize: 10,
        orderNo: '',
        stuffDescribe: '',
        status: ''
      }
      this.search()
    },

    /**
     * 查看详情
     */
    viewDetail(row) {
      getGoodStuffOrder(row.id).then(res => {
        this.detailDialog.data = res.data.data
        this.detailDialog.visible = true
      }).catch(err => {
        console.error('获取订单详情失败:', err)
        this.$message.error(err.data?.errorMessage || '获取订单详情失败')
      })
    },

    /**
     * 删除订单
     */
    deleted(id) {
      this.$confirm('确定要删除这个订单吗？删除后无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteGoodStuffOrder(id).then(() => {
          this.search()
          this.$message.success('删除成功')
        }).catch(err => {
          console.error('删除订单失败:', err)
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {
        // 用户取消删除
      })
    },

    /**
     * 分页变化
     */
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    },

    /**
     * 获取商品快照信息
     */
    getStuffSnapshot(order) {
      if (!order.stuffSnapshot) return null
      try {
        return JSON.parse(order.stuffSnapshot)
      } catch (e) {
        console.error('解析商品快照失败:', e)
        return null
      }
    },

    /**
     * 获取商品信息
     */
    getStuffInfo(order, field) {
      const snapshot = this.getStuffSnapshot(order)
      return snapshot ? snapshot[field] || '-' : '-'
    },

    /**
     * 获取商品图片列表
     */
    getStuffImages(order) {
      const snapshot = this.getStuffSnapshot(order)
      if (!snapshot || !snapshot.media) return []

      // 处理逗号分隔的图片
      const images = snapshot.media.split(',').map(img => img.trim()).filter(img => img)
      return images.map(img => {
        // 如果是相对路径，添加基础URL
        if (img.startsWith('assets/')) {
          return `${this.$baseURL || ''}/${img}`
        }
        return img
      })
    },

    /**
     * 获取状态标签
     */
    getStatusLabel(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const typeMap = {
        'pending': 'warning',
        'processing': 'primary',
        'complete': 'success',
        'cancelled': 'danger'
      }
      return typeMap[status] || 'info'
    },

    /**
     * 获取状态颜色
     */
    getStatusColor(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.cssClass : '#909399'
    },

    /**
     * 加载状态字典
     */
    async loadStatusOptions() {
      try {
        const res = await listDictByNameEn('good_stuff_order_status')
        this.statusOptions = (res.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn,
          cssClass: item.cssClass
        }))
      } catch (err) {
        console.error('加载订单状态字典失败:', err)
        // 使用默认状态
        this.statusOptions = [
          { label: '待处理', value: 'pending', cssClass: '#E6A23C' },
          { label: '处理中', value: 'processing', cssClass: '#409EFF' },
          { label: '已完成', value: 'complete', cssClass: '#67C23A' },
          { label: '已取消', value: 'cancelled', cssClass: '#F56C6C' }
        ]
      }
    }
  },

  async created() {
    // 加载状态字典
    await this.loadStatusOptions()
    // 搜索订单列表
    this.search()
  }
}
</script>

<style scoped>
.order-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.order-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

.card--search {
  flex-shrink: 0;
}

.card--table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.price-text {
  color: #e74c3c;
  font-weight: bold;
}

.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

.description-text {
  max-height: 100px;
  overflow-y: auto;
  line-height: 1.5;
  word-break: break-all;
}

/* 商品信息样式 */
.goods-info {
  text-align: left;
}

.goods-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goods-address {
  font-size: 12px;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 图片展示样式 */
.image-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.image-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-gallery .el-image {
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.image-gallery .el-image:hover {
  transform: scale(1.05);
}

/* 暗色主题适配 */
.dark-theme .card {
  background: var(--card-background);
  color: var(--text-primary);
}

.dark-theme .price-text {
  color: #ff6b6b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-flex {
    flex-direction: column;
    align-items: stretch;
  }

  .search-flex > * {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .search-flex > *:last-child {
    margin-bottom: 0;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
